version: '3.9'

services:
  redis:
    image: redis:6.2.5
    container_name: redis
    command: ["redis-server", "--requirepass", "El9M^l)1|*d!G33W"]
    volumes:
      - ./redis:/var/lib/redis
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - heineken_net

  redis-insight:
    image: redislabs/redisinsight
    container_name: redis-insight
    restart: always
    ports:
      - "5540:5540"
    volumes:
      - redis_insight_volume_data:/db
    networks:
      - heineken_net

  heineken:
    build:
      context: .
      dockerfile: Dockerfile
    image: heineken/app:latest
    container_name: heineken
    env_file:
      - .env
    command: >
      bash -c "
        /app/scripts/prestart.sh && 
        celery -A app.celery_app worker --loglevel=info --concurrency=2 --logfile=logs/celery_worker.log &
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 1
      "
    ports:
      - "8000:8000"
    depends_on:
      - redis
    networks:
      - heineken_net
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: El9M^l)1|*d!G33W
      FIRST_SUPERUSER: '<EMAIL>'
      FIRST_SUPERUSER_PASSWORD: 12e634a8dn62

networks:
  heineken_net:
    driver: bridge

volumes:
  redis:
  redis_insight_volume_data:

