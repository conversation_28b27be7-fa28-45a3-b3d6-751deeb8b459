"""Add conversation_logs table

Revision ID: 006_add_conversation_logs_table
Revises: 005_add_conversational_chatbot_tables
Create Date: 2025-07-23 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision: str = '006_add_conversation_logs_table'
down_revision: Union[str, None] = '005_add_conversational_chatbot_tables'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add conversation_logs table for storing SMS conversation messages"""
    
    # Create conversation_logs table
    op.create_table(
        'conversation_logs',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.func.gen_random_uuid(), 
                  comment="Unique identifier for the conversation log entry"),
        sa.Column('lead_id', UUID(as_uuid=True), sa.<PERSON>('leads.id', ondelete='CASCADE'), 
                  nullable=False, comment="Reference to the lead this conversation belongs to"),
        sa.Column('sender', sa.String(10), nullable=False, 
                  comment="Message sender: 'user' for customer messages, 'system' for AI responses"),
        sa.Column('message', sa.Text(), nullable=False, 
                  comment="The actual message content"),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False, 
                  server_default=sa.func.now(), comment="When the message was sent/received"),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, 
                  comment="Whether this conversation log entry is active"),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False, 
                  comment="Whether this conversation log entry is deleted"),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, 
                  server_default=sa.func.now(), comment="When this conversation log entry was created"),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, 
                  server_default=sa.func.now(), onupdate=sa.func.now(), 
                  comment="When this conversation log entry was last updated"),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True, 
                  comment="When this conversation log entry was deleted"),
    )
    
    # Add check constraint for sender values
    op.create_check_constraint(
        'ck_conversation_logs_sender',
        'conversation_logs',
        "sender IN ('user', 'system')"
    )
    
    # Create indexes for performance
    op.create_index('ix_conversation_logs_lead_id', 'conversation_logs', ['lead_id'])
    op.create_index('ix_conversation_logs_sender', 'conversation_logs', ['sender'])
    op.create_index('ix_conversation_logs_timestamp', 'conversation_logs', ['timestamp'])
    op.create_index('ix_conversation_logs_is_active', 'conversation_logs', ['is_active'])
    op.create_index('ix_conversation_logs_is_deleted', 'conversation_logs', ['is_deleted'])
    
    # Create composite indexes for common query patterns
    op.create_index('idx_conversation_logs_lead_timestamp', 'conversation_logs', ['lead_id', 'timestamp'])
    op.create_index('idx_conversation_logs_sender_timestamp', 'conversation_logs', ['sender', 'timestamp'])
    op.create_index('idx_conversation_logs_active_deleted', 'conversation_logs', ['is_active', 'is_deleted'])
    op.create_index('idx_conversation_logs_lead_sender', 'conversation_logs', ['lead_id', 'sender'])


def downgrade() -> None:
    """Remove conversation_logs table"""
    
    # Drop indexes
    op.drop_index('idx_conversation_logs_lead_sender', table_name='conversation_logs')
    op.drop_index('idx_conversation_logs_active_deleted', table_name='conversation_logs')
    op.drop_index('idx_conversation_logs_sender_timestamp', table_name='conversation_logs')
    op.drop_index('idx_conversation_logs_lead_timestamp', table_name='conversation_logs')
    op.drop_index('ix_conversation_logs_is_deleted', table_name='conversation_logs')
    op.drop_index('ix_conversation_logs_is_active', table_name='conversation_logs')
    op.drop_index('ix_conversation_logs_timestamp', table_name='conversation_logs')
    op.drop_index('ix_conversation_logs_sender', table_name='conversation_logs')
    op.drop_index('ix_conversation_logs_lead_id', table_name='conversation_logs')
    
    # Drop check constraint
    op.drop_constraint('ck_conversation_logs_sender', 'conversation_logs', type_='check')
    
    # Drop table
    op.drop_table('conversation_logs')
