"""merge_multiple_heads

Revision ID: 1603bf119403
Revises: 002_add_holiday_and_messaging_rule_tables, 003pgvector, 004_add_document_processing_status, 006_add_conversation_logs_table, 46c979418266
Create Date: 2025-07-23 14:01:17.441966

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '1603bf119403'
down_revision: Union[str, None] = ('002_add_holiday_and_messaging_rule_tables', '003pgvector', '004_add_document_processing_status', '006_add_conversation_logs_table', '46c979418266')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
