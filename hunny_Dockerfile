FROM python:3.11
 
# Disable buffering
ENV PYTHONUNBUFFERED=1

# Install nano and vi text editors
RUN apt-get update && apt-get install -y \
    nano \
    vim \
    && rm -rf /var/lib/apt/lists/*
 
# Set working directory
WORKDIR /app/

COPY . .

# Install uv from the official image
COPY --from=ghcr.io/astral-sh/uv:0.5.11 /uv /uvx /bin/
 
# Set up virtual environment path and config
ENV PATH="/app/.venv/bin:$PATH"
ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy
 
# Install dependencies using uv
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project
 
# Set PYTHONPATH
ENV PYTHONPATH=/app
 
# Copy project files
COPY ./scripts /app/scripts
COPY ./pyproject.toml ./uv.lock ./alembic.ini /app/
COPY ./app /app/app
 
# Install project dependencies again after code is copied
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync
 
# Make prestart script executable
RUN chmod +x /app/scripts/prestart.sh

EXPOSE 8000

# Run prestart script and app at container startup
#CMD ["bash", "-c", "/app/scripts/prestart.sh && fastapi run --workers 1 app/main.py"]

CMD ["bash", "-c", "/app/scripts/prestart.sh && celery -A app.celery_app worker --loglevel=info --concurrency=2 --logfile=logs/celery_worker.log & uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4"]
