# GrowthHive FastAPI Backend - Clean Dependencies
# Core Framework
fastapi==0.116.1
uvicorn[standard]==0.35.0
starlette==0.47.2

# Database & ORM
sqlalchemy==2.0.41
asyncpg==0.30.0
alembic==1.16.4
greenlet==3.2.3
psycopg[binary]==3.2.9
psycopg2-binary==2.9.10
pgvector==0.4.1

# Authentication & Security
python-jose[cryptography]==3.5.0
passlib[bcrypt]==1.7.4
bcrypt==4.3.0
python-multipart==0.0.20
PyJWT==2.10.1

# Data Validation & Settings
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
email-validator==2.2.0

# HTTP Client & Testing
httpx==0.28.1
requests==2.32.4
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-cov==6.2.1
moto[s3]==5.1.8
aiohttp==3.12.14

# File Handling & Storage
aiofiles==24.1.0
boto3==1.39.11
botocore==1.39.11

# AI & OpenAI
openai==1.97.1
numpy==2.2.6
scikit-learn==1.7.1

# Multi-Agent Framework
langgraph==0.5.4
langgraph-checkpoint==2.1.1
langgraph-prebuilt==0.5.2
langgraph-sdk==0.1.74
langchain==0.3.26
langchain-community==0.3.27
langchain-core==0.3.71
langchain-openai==0.3.28
langchain-text-splitters==0.3.8

# Vector Database & Embeddings
chromadb==1.0.15
faiss-cpu==1.11.0.post1

# Memory & Caching
redis==6.2.0

# Additional AI Tools
tiktoken==0.9.0

# Utilities
python-dotenv==1.1.1
python-dateutil==2.9.0.post0
pytz==2025.2
phonenumbers==9.0.10
tenacity==9.1.2

# Development & Code Quality
ruff==0.12.4
mypy==1.17.0
black==25.1.0
isort==6.0.1

# Logging & Monitoring
structlog==25.4.0

# DocQA CLI Dependencies
typer==0.16.0
rich==14.0.0
click==8.2.1

# Document Processing
unstructured==0.18.9
unstructured-client==0.39.1
unstructured-inference==1.0.5
unstructured.pytesseract==0.3.15
pytesseract==0.3.13
pillow==11.3.0
PyMuPDF==1.26.3
python-docx==1.2.0
openpyxl==3.1.5
xlsxwriter==3.2.5
xlrd==2.0.2

# Image Processing (ACTUALLY USED)
opencv-python==4.12.0.88

# PDF Processing
pypdf==5.8.0
pypdfium2==4.30.0
pdf2image==1.17.0
pdfminer.six==20250506

# PowerPoint Processing
python-pptx==1.0.2

# Background Task Processing
celery==5.5.3
kombu==5.5.4
billiard==4.2.1
vine==5.1.0

# Document Processing
python-magic==0.4.27

# Additional Utilities
aiohappyeyeballs==2.6.1
aiosignal==1.4.0
anyio==4.9.0
attrs==25.3.0
backoff==2.2.1
beautifulsoup4==4.13.4
cachetools==5.5.2
certifi==2025.7.14
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
click-didyoumean==0.3.1
click-plugins==1.1.1.2
click-repl==0.3.0
coloredlogs==15.0.1
contourpy==1.3.2
coverage==7.9.2
cryptography==45.0.5
cycler==0.12.1
dataclasses-json==0.6.7
Deprecated==1.2.18
distro==1.9.0
dnspython==2.7.0
durationpy==0.10
ecdsa==0.19.1
emoji==2.14.1
et_xmlfile==2.0.0
filelock==3.18.0
filetype==1.2.0
flatbuffers==25.2.10
fonttools==4.59.0
frozenlist==1.7.0
fsspec==2025.7.0
google-api-core==2.25.1
google-auth==2.40.3
google-cloud-vision==3.10.2
googleapis-common-protos==1.70.0
grpcio==1.73.1
grpcio-status==1.73.1
h11==0.16.0
hf-xet==1.1.5
html5lib==1.1
httpcore==1.0.9
httptools==0.6.4
httpx-sse==0.4.1
huggingface-hub==0.33.4
humanfriendly==10.0
idna==3.10
importlib_metadata==8.7.0
importlib_resources==6.5.2
iniconfig==2.1.0
Jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
kubernetes==33.1.0
langdetect==1.0.9
langsmith==0.4.8
lxml==6.0.0
Mako==1.3.10
Markdown==3.8.2
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
mdurl==0.1.2
mmh3==5.1.0
mpmath==1.3.0
msoffcrypto-tool==5.4.2
multidict==6.6.3
mypy_extensions==1.1.0
nest-asyncio==1.6.0
networkx==3.5
nltk==3.9.1
oauthlib==3.3.1
olefile==0.47
omegaconf==2.3.0
onnx==1.18.0
onnxruntime==1.22.1
opentelemetry-api==1.35.0
opentelemetry-exporter-otlp-proto-common==1.35.0
opentelemetry-exporter-otlp-proto-grpc==1.35.0
opentelemetry-proto==1.35.0
opentelemetry-sdk==1.35.0
opentelemetry-semantic-conventions==0.56b0
orjson==3.11.0
ormsgpack==1.10.0
overrides==7.7.0
packaging==25.0
pandas==2.3.1
pathspec==0.12.1
pi_heif==1.0.0
pikepdf==9.10.2
platformdirs==4.3.8
pluggy==1.6.0
posthog==5.4.0
prompt_toolkit==3.0.51
propcache==0.3.2
proto-plus==1.26.1
protobuf==6.31.1
psutil==7.0.0
py-partiql-parser==0.6.1
pyasn1==0.6.1
pyasn1_modules==0.4.2
pybase64==1.4.1
pycocotools==2.0.10
pycparser==2.22
Pygments==2.19.2
pyparsing==3.2.3
pypandoc==1.15
python-iso639==2025.2.18
python-oxmsg==0.0.2
pytz==2025.2
PyYAML==6.0.2
RapidFuzz==3.13.0
referencing==0.36.2
regex==2024.11.6
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
responses==0.25.7
rpds-py==0.26.0
rsa==4.9.1
s3transfer==0.13.1
safetensors==0.5.3
scipy==1.16.0
setuptools==80.9.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
sympy==1.14.0
threadpoolctl==3.6.0
tokenizers==0.21.2
tqdm==4.67.1
tzdata==2025.2
urllib3==2.5.0
uvloop==0.21.0
watchfiles==1.1.0
wcwidth==0.2.13
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.1.3
wrapt==1.17.2
xmltodict==0.14.2
xxhash==3.5.0
yarl==1.20.1
zipp==3.23.0
zstandard==0.23.0 