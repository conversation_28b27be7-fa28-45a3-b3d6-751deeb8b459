#!/bin/bash

echo "🧹 Cleaning GrowthHive Dependencies"
echo "====================================="

# Remove unused PyTorch dependencies
echo "🗑️  Removing unused PyTorch dependencies..."
pip uninstall -y torch torchvision transformers timm effdet accelerate

# Remove other potentially unused ML dependencies
echo "🗑️  Removing other unused ML dependencies..."
pip uninstall -y torchaudio torchtext torchdata torchserve torchx

# Install cleaned requirements
echo "📦 Installing cleaned requirements..."
pip install -r requirements_clean.txt

echo "✅ Dependencies cleaned successfully!"
echo ""
echo "📊 Summary:"
echo "- Removed PyTorch ecosystem (~2GB+ of unused dependencies)"
echo "- Kept all actively used packages (OpenCV, OpenAI, LangChain, etc.)"
echo "- Maintained exact versions for stability"
echo ""
echo "🎯 Your project now uses:"
echo "- OpenAI APIs for AI functionality"
echo "- OpenCV for image processing"
echo "- LangChain/LangGraph for multi-agent system"
echo "- pgvector for vector storage"
echo ""
echo "💾 Clean requirements saved as: requirements_clean.txt" 