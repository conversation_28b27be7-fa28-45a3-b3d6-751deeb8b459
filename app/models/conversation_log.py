"""
Conversation Log Model
SQLAlchemy model for storing conversation messages between users and the system
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import String, Boolean, DateTime, Text, func, Index, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import ForeignKey
from app.core.database.connection import Base


class ConversationLog(Base):
    """Conversation log model for storing SMS conversation messages"""

    __tablename__ = "conversation_logs"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
        comment="Unique identifier for the conversation log entry"
    )

    # Lead association
    lead_id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("leads.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="Reference to the lead this conversation belongs to"
    )

    # Message sender
    sender: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        index=True,
        comment="Message sender: 'user' for customer messages, 'system' for AI responses"
    )

    # Message content
    message: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="The actual message content"
    )

    # Timestamp
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(),
        server_default=func.now(),
        index=True,
        comment="When the message was sent/received"
    )

    # Standard fields following application conventions
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        index=True,
        comment="Whether this conversation log entry is active"
    )

    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        comment="Whether this conversation log entry is deleted"
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="When this conversation log entry was created"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="When this conversation log entry was last updated"
    )

    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="When this conversation log entry was deleted"
    )

    # Relationships
    lead = relationship("Lead", back_populates="conversation_logs", lazy="select")

    # Table constraints
    __table_args__ = (
        # Check constraint for sender values
        CheckConstraint(
            "sender IN ('user', 'system')",
            name="ck_conversation_logs_sender"
        ),
        # Composite indexes for performance
        Index('idx_conversation_logs_lead_timestamp', 'lead_id', 'timestamp'),
        Index('idx_conversation_logs_sender_timestamp', 'sender', 'timestamp'),
        Index('idx_conversation_logs_active_deleted', 'is_active', 'is_deleted'),
        Index('idx_conversation_logs_lead_sender', 'lead_id', 'sender'),
    )

    def __repr__(self) -> str:
        return (
            f"<ConversationLog(id={self.id}, lead_id={self.lead_id}, "
            f"sender={self.sender}, timestamp={self.timestamp})>"
        )

    def __str__(self) -> str:
        return f"ConversationLog {self.id}: {self.sender} - {self.message[:50]}..."
