"""
Conversation Message Model
SQLAlchemy model for storing individual SMS conversation messages
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import String, Boolean, DateTime, Text, Integer, ForeignKey, func, Index
from sqlalchemy.dialects.postgresql import UUID as PGUUID, JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.core.database.connection import Base


class ConversationMessage(Base):
    """Individual conversation message model for storing SMS chat history"""

    __tablename__ = "conversation_messages"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Session association
    session_id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("conversation_sessions.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="Reference to the conversation session",
    )

    # Message details
    message_content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="The actual message content (user input or AI response)",
    )

    message_type: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
        comment="Type of message: 'user_input' or 'ai_response'",
    )

    # Message metadata
    phone_number: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
        comment="Phone number for quick filtering (denormalized for performance)",
    )

    message_sequence: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        comment="Sequential number of message within the session (1, 2, 3...)",
    )

    # AI processing metadata
    ai_processing_time_ms: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="Time taken to generate AI response in milliseconds",
    )

    ai_model_used: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="AI model used for response generation (e.g., 'gpt-4', 'claude-3')",
    )

    rag_sources_used: Mapped[Optional[dict]] = mapped_column(
        JSONB,
        nullable=True,
        comment="RAG sources and metadata used for AI response generation",
    )

    conversation_stage: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        index=True,
        comment="Conversation stage when message was sent (greeting, prequalification, etc.)",
    )

    # Webhook integration
    webhook_id: Mapped[Optional[UUID]] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("webhooks.id"),
        nullable=True,
        comment="Reference to the webhook that triggered this message (for user inputs)",
    )

    webhook_message_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True,
        comment="External message ID from webhook provider (Kudosity)",
    )

    # Message status and delivery
    delivery_status: Mapped[Optional[str]] = mapped_column(
        String(20),
        nullable=True,
        index=True,
        comment="Delivery status for AI responses: 'pending', 'sent', 'delivered', 'failed'",
    )

    delivery_timestamp: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="When the message was actually delivered (for AI responses)",
    )

    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Error message if delivery failed",
    )

    # Standard audit fields
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="Whether this message record is active",
    )

    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        comment="Whether this message record is deleted",
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="When this message was created/received",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="When this message record was last updated",
    )

    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="When this message record was deleted",
    )

    # Relationships
    session = relationship("ConversationSession", back_populates="messages", lazy="select")
    webhook = relationship("Webhook", lazy="select")

    # Composite indexes for performance
    __table_args__ = (
        Index('idx_conversation_messages_session_sequence', 'session_id', 'message_sequence'),
        Index('idx_conversation_messages_phone_created', 'phone_number', 'created_at'),
        Index('idx_conversation_messages_type_stage', 'message_type', 'conversation_stage'),
        Index('idx_conversation_messages_delivery', 'delivery_status', 'delivery_timestamp'),
    )

    def __repr__(self) -> str:
        return (
            f"<ConversationMessage(id={self.id}, session_id={self.session_id}, "
            f"type={self.message_type}, sequence={self.message_sequence}, "
            f"content='{self.message_content[:50]}...')>"
        )

    @property
    def is_user_message(self) -> bool:
        """Check if this is a user message"""
        return self.message_type == "user_input"

    @property
    def is_ai_message(self) -> bool:
        """Check if this is an AI response"""
        return self.message_type == "ai_response"
