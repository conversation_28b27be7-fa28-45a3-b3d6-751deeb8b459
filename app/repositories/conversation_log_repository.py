"""
Conversation Log Repository
Database operations for conversation log management
"""

from datetime import datetime
from typing import Optional, List, Tuple
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, desc, asc, func
from sqlalchemy.orm import selectinload
from app.models.conversation_log import ConversationLog
from app.schemas.conversation_log import ConversationLogCreate, ConversationLogUpdate, ConversationLogFilter
import logging

logger = logging.getLogger(__name__)


class ConversationLogRepository:
    """Repository for conversation log operations."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create(self, conversation_log_data: ConversationLogCreate) -> ConversationLog:
        """
        Create a new conversation log entry.
        
        Args:
            conversation_log_data: Conversation log creation data
            
        Returns:
            ConversationLog: Created conversation log entry
        """
        try:
            # Create conversation log instance
            conversation_log = ConversationLog(
                lead_id=conversation_log_data.lead_id,
                sender=conversation_log_data.sender.value,
                message=conversation_log_data.message,
                timestamp=conversation_log_data.timestamp or datetime.now(),
                is_active=True,
                is_deleted=False
            )
            
            self.db.add(conversation_log)
            await self.db.commit()
            await self.db.refresh(conversation_log)
            
            logger.info(f"Created conversation log entry: {conversation_log.id}")
            return conversation_log
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating conversation log entry: {e}")
            raise

    async def get_by_id(self, conversation_log_id: UUID) -> Optional[ConversationLog]:
        """
        Get a conversation log entry by ID.
        
        Args:
            conversation_log_id: Conversation log entry ID
            
        Returns:
            ConversationLog object or None
        """
        try:
            query = select(ConversationLog).options(
                selectinload(ConversationLog.lead)
            ).where(
                and_(
                    ConversationLog.id == conversation_log_id,
                    ConversationLog.is_deleted == False
                )
            )
            result = await self.db.execute(query)
            conversation_log = result.scalar_one_or_none()
            return conversation_log
        except Exception as e:
            logger.error(f"Error fetching conversation log by ID {conversation_log_id}: {e}")
            raise

    async def get_by_lead_id(
        self, 
        lead_id: UUID, 
        limit: int = 50, 
        offset: int = 0,
        include_deleted: bool = False
    ) -> List[ConversationLog]:
        """
        Get conversation logs by lead ID.
        
        Args:
            lead_id: Lead ID to filter by
            limit: Maximum number of entries to return
            offset: Number of entries to skip
            include_deleted: Whether to include deleted entries
            
        Returns:
            List of ConversationLog objects
        """
        try:
            conditions = [ConversationLog.lead_id == lead_id]
            if not include_deleted:
                conditions.append(ConversationLog.is_deleted == False)
            
            query = select(ConversationLog).where(
                and_(*conditions)
            ).order_by(desc(ConversationLog.timestamp)).limit(limit).offset(offset)
            
            result = await self.db.execute(query)
            conversation_logs = result.scalars().all()
            return list(conversation_logs)
        except Exception as e:
            logger.error(f"Error fetching conversation logs by lead ID {lead_id}: {e}")
            raise

    async def list_with_filters(
        self, 
        filters: ConversationLogFilter,
        limit: int = 50,
        offset: int = 0,
        order_by: str = "timestamp",
        order_direction: str = "desc"
    ) -> Tuple[List[ConversationLog], int]:
        """
        List conversation logs with filters and pagination.
        
        Args:
            filters: Filter criteria
            limit: Maximum number of entries to return
            offset: Number of entries to skip
            order_by: Field to order by
            order_direction: Order direction (asc/desc)
            
        Returns:
            Tuple of (conversation logs list, total count)
        """
        try:
            # Build filter conditions
            conditions = []
            
            if filters.lead_id:
                conditions.append(ConversationLog.lead_id == filters.lead_id)
            
            if filters.sender:
                conditions.append(ConversationLog.sender == filters.sender.value)
            
            if filters.start_date:
                conditions.append(ConversationLog.timestamp >= filters.start_date)
            
            if filters.end_date:
                conditions.append(ConversationLog.timestamp <= filters.end_date)
            
            if filters.is_active is not None:
                conditions.append(ConversationLog.is_active == filters.is_active)
            else:
                # Default to not showing deleted entries
                conditions.append(ConversationLog.is_deleted == False)
            
            if filters.search:
                conditions.append(ConversationLog.message.ilike(f"%{filters.search}%"))
            
            # Build query
            base_query = select(ConversationLog).where(and_(*conditions))
            
            # Get total count
            count_query = select(func.count()).select_from(ConversationLog).where(and_(*conditions))
            count_result = await self.db.execute(count_query)
            total = count_result.scalar()
            
            # Apply ordering
            order_column = getattr(ConversationLog, order_by, ConversationLog.timestamp)
            if order_direction.lower() == "asc":
                base_query = base_query.order_by(asc(order_column))
            else:
                base_query = base_query.order_by(desc(order_column))
            
            # Apply pagination
            query = base_query.limit(limit).offset(offset)
            
            result = await self.db.execute(query)
            conversation_logs = result.scalars().all()
            
            return list(conversation_logs), total
            
        except Exception as e:
            logger.error(f"Error listing conversation logs with filters: {e}")
            raise

    async def update(self, conversation_log_id: UUID, update_data: ConversationLogUpdate) -> Optional[ConversationLog]:
        """
        Update a conversation log entry.
        
        Args:
            conversation_log_id: Conversation log entry ID
            update_data: Update data
            
        Returns:
            Updated ConversationLog object or None
        """
        try:
            # Check if conversation log exists
            existing_log = await self.get_by_id(conversation_log_id)
            if not existing_log:
                return None
            
            # Build update values
            update_values = {}
            if update_data.message is not None:
                update_values['message'] = update_data.message
            if update_data.is_active is not None:
                update_values['is_active'] = update_data.is_active
            
            if not update_values:
                return existing_log
            
            # Update the conversation log
            query = update(ConversationLog).where(
                ConversationLog.id == conversation_log_id
            ).values(**update_values)
            
            await self.db.execute(query)
            await self.db.commit()
            
            # Return updated conversation log
            return await self.get_by_id(conversation_log_id)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating conversation log {conversation_log_id}: {e}")
            raise

    async def soft_delete(self, conversation_log_id: UUID) -> bool:
        """
        Soft delete a conversation log entry.
        
        Args:
            conversation_log_id: Conversation log entry ID
            
        Returns:
            bool: True if deleted successfully, False if not found
        """
        try:
            # Check if conversation log exists
            existing_log = await self.get_by_id(conversation_log_id)
            if not existing_log:
                return False
            
            # Soft delete
            query = update(ConversationLog).where(
                ConversationLog.id == conversation_log_id
            ).values(
                is_deleted=True,
                deleted_at=datetime.now(),
                is_active=False
            )
            
            await self.db.execute(query)
            await self.db.commit()
            
            logger.info(f"Soft deleted conversation log: {conversation_log_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error soft deleting conversation log {conversation_log_id}: {e}")
            raise

    async def hard_delete(self, conversation_log_id: UUID) -> bool:
        """
        Hard delete a conversation log entry.
        
        Args:
            conversation_log_id: Conversation log entry ID
            
        Returns:
            bool: True if deleted successfully, False if not found
        """
        try:
            # Check if conversation log exists
            existing_log = await self.get_by_id(conversation_log_id)
            if not existing_log:
                return False
            
            # Hard delete
            query = delete(ConversationLog).where(ConversationLog.id == conversation_log_id)
            await self.db.execute(query)
            await self.db.commit()
            
            logger.info(f"Hard deleted conversation log: {conversation_log_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error hard deleting conversation log {conversation_log_id}: {e}")
            raise
