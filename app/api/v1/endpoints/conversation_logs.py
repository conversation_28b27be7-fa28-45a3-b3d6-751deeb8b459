"""
Conversation Logs API endpoints
Authenticated endpoints for managing conversation logs
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse

from app.schemas.conversation_log import (
    ConversationLogResponse,
    ConversationLogListResponse,
    ConversationLogCreate,
    ConversationLogUpdate,
    ConversationLogFilter,
    SenderType
)
from app.services.conversation_log_service import ConversationLogService
from app.core.factory import get_conversation_log_service
from app.core.security.enhanced_auth_middleware import get_current_active_user
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.core.logging import logger

router = APIRouter()


@router.get(
    "/",
    response_model=Dict[str, Any],
    summary="List conversation logs",
    description="Get a paginated list of conversation logs with optional filters",
    responses={
        200: {"description": "Conversation logs retrieved successfully"},
        401: {"description": "Authentication required"},
        403: {"description": "Insufficient permissions"},
        500: {"description": "Internal server error"}
    }
)
async def list_conversation_logs(
    lead_id: Optional[UUID] = Query(None, description="Filter by lead ID"),
    sender: Optional[SenderType] = Query(None, description="Filter by sender type"),
    start_date: Optional[datetime] = Query(None, description="Filter messages from this date"),
    end_date: Optional[datetime] = Query(None, description="Filter messages until this date"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search in message content"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(50, ge=1, le=100, description="Items per page"),
    order_by: str = Query("timestamp", description="Field to order by"),
    order_direction: str = Query("desc", regex="^(asc|desc)$", description="Order direction"),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
):
    """List conversation logs with pagination and filters"""
    try:
        # Create filter object
        filters = ConversationLogFilter(
            lead_id=lead_id,
            sender=sender,
            start_date=start_date,
            end_date=end_date,
            is_active=is_active,
            search=search
        )
        
        # Calculate offset
        offset = (page - 1) * per_page
        
        # Get conversation logs
        conversation_logs, total = await conversation_log_service.list_conversations(
            filters=filters,
            limit=per_page,
            offset=offset,
            order_by=order_by,
            order_direction=order_direction
        )
        
        # Convert to response format
        items = [ConversationLogResponse.from_orm(log) for log in conversation_logs]
        
        # Create pagination info
        has_next = offset + per_page < total
        has_prev = page > 1
        
        response_data = ConversationLogListResponse(
            items=items,
            total=total,
            page=page,
            per_page=per_page,
            has_next=has_next,
            has_prev=has_prev
        )
        
        return create_success_response(
            message_title="Conversation Logs Retrieved",
            message_description=f"Retrieved {len(items)} conversation logs",
            data=response_data.dict()
        )
        
    except Exception as e:
        logger.error(f"Error listing conversation logs: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Error Listing Conversation Logs",
            message_description="An error occurred while retrieving conversation logs",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/{conversation_log_id}",
    response_model=Dict[str, Any],
    summary="Get conversation log by ID",
    description="Retrieve a specific conversation log entry by ID",
    responses={
        200: {"description": "Conversation log retrieved successfully"},
        401: {"description": "Authentication required"},
        403: {"description": "Insufficient permissions"},
        404: {"description": "Conversation log not found"},
        500: {"description": "Internal server error"}
    }
)
async def get_conversation_log(
    conversation_log_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
):
    """Get a conversation log entry by ID"""
    try:
        conversation_log = await conversation_log_service.get_by_id(conversation_log_id)
        
        if not conversation_log:
            return create_error_response(
                error_code=ErrorCodes.RESOURCE_NOT_FOUND,
                message_title="Conversation Log Not Found",
                message_description=f"Conversation log with ID {conversation_log_id} not found",
                status_code=status.HTTP_404_NOT_FOUND
            )
        
        response_data = ConversationLogResponse.from_orm(conversation_log)
        
        return create_success_response(
            message_title="Conversation Log Retrieved",
            message_description="Conversation log retrieved successfully",
            data=response_data.dict()
        )
        
    except Exception as e:
        logger.error(f"Error getting conversation log {conversation_log_id}: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Error Retrieving Conversation Log",
            message_description="An error occurred while retrieving the conversation log",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/lead/{lead_id}",
    response_model=Dict[str, Any],
    summary="Get conversation history for a lead",
    description="Retrieve conversation history for a specific lead",
    responses={
        200: {"description": "Conversation history retrieved successfully"},
        401: {"description": "Authentication required"},
        403: {"description": "Insufficient permissions"},
        500: {"description": "Internal server error"}
    }
)
async def get_lead_conversation_history(
    lead_id: UUID,
    limit: int = Query(50, ge=1, le=100, description="Maximum number of messages"),
    offset: int = Query(0, ge=0, description="Number of messages to skip"),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
):
    """Get conversation history for a specific lead"""
    try:
        conversation_logs = await conversation_log_service.get_conversation_history(
            lead_id=lead_id,
            limit=limit,
            offset=offset
        )
        
        # Convert to response format
        items = [ConversationLogResponse.from_orm(log) for log in conversation_logs]
        
        # Get conversation stats
        stats = await conversation_log_service.get_conversation_stats(lead_id)
        
        return create_success_response(
            message_title="Conversation History Retrieved",
            message_description=f"Retrieved {len(items)} conversation messages for lead",
            data={
                "messages": [item.dict() for item in items],
                "stats": stats,
                "lead_id": str(lead_id),
                "limit": limit,
                "offset": offset
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting conversation history for lead {lead_id}: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Error Retrieving Conversation History",
            message_description="An error occurred while retrieving conversation history",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put(
    "/{conversation_log_id}",
    response_model=Dict[str, Any],
    summary="Update conversation log",
    description="Update a conversation log entry",
    responses={
        200: {"description": "Conversation log updated successfully"},
        401: {"description": "Authentication required"},
        403: {"description": "Insufficient permissions"},
        404: {"description": "Conversation log not found"},
        422: {"description": "Validation error"},
        500: {"description": "Internal server error"}
    }
)
async def update_conversation_log(
    conversation_log_id: UUID,
    update_data: ConversationLogUpdate,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
):
    """Update a conversation log entry"""
    try:
        updated_log = await conversation_log_service.update_message(
            conversation_log_id=conversation_log_id,
            update_data=update_data
        )
        
        if not updated_log:
            return create_error_response(
                error_code=ErrorCodes.RESOURCE_NOT_FOUND,
                message_title="Conversation Log Not Found",
                message_description=f"Conversation log with ID {conversation_log_id} not found",
                status_code=status.HTTP_404_NOT_FOUND
            )
        
        response_data = ConversationLogResponse.from_orm(updated_log)
        
        return create_success_response(
            message_title="Conversation Log Updated",
            message_description="Conversation log updated successfully",
            data=response_data.dict()
        )
        
    except Exception as e:
        logger.error(f"Error updating conversation log {conversation_log_id}: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Error Updating Conversation Log",
            message_description="An error occurred while updating the conversation log",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete(
    "/{conversation_log_id}",
    response_model=Dict[str, Any],
    summary="Delete conversation log",
    description="Soft delete a conversation log entry",
    responses={
        200: {"description": "Conversation log deleted successfully"},
        401: {"description": "Authentication required"},
        403: {"description": "Insufficient permissions"},
        404: {"description": "Conversation log not found"},
        500: {"description": "Internal server error"}
    }
)
async def delete_conversation_log(
    conversation_log_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    conversation_log_service: ConversationLogService = Depends(get_conversation_log_service)
):
    """Soft delete a conversation log entry"""
    try:
        deleted = await conversation_log_service.delete_message(conversation_log_id)
        
        if not deleted:
            return create_error_response(
                error_code=ErrorCodes.RESOURCE_NOT_FOUND,
                message_title="Conversation Log Not Found",
                message_description=f"Conversation log with ID {conversation_log_id} not found",
                status_code=status.HTTP_404_NOT_FOUND
            )
        
        return create_success_response(
            message_title="Conversation Log Deleted",
            message_description="Conversation log deleted successfully",
            data={"deleted_id": str(conversation_log_id)}
        )
        
    except Exception as e:
        logger.error(f"Error deleting conversation log {conversation_log_id}: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Error Deleting Conversation Log",
            message_description="An error occurred while deleting the conversation log",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
