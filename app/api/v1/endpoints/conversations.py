"""
Conversation History API Endpoints
FastAPI endpoints for retrieving SMS conversation history
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field
import structlog

from app.services.conversation_message_service import ConversationMessageService
from app.models.conversation_message import ConversationMessage
from app.models.conversation_session import ConversationSession
from app.core.database.connection import get_db_session
from app.core.security.auth import get_current_user
from app.core.responses import create_success_response, create_error_response, ErrorCodes

logger = structlog.get_logger()
router = APIRouter()


# Response Models
class ConversationMessageResponse(BaseModel):
    """Response model for conversation message"""
    id: UUID
    session_id: UUID
    message_content: str
    message_type: str
    phone_number: str
    message_sequence: int
    conversation_stage: Optional[str]
    ai_processing_time_ms: Optional[int]
    ai_model_used: Optional[str]
    rag_sources_used: Optional[Dict[str, Any]]
    delivery_status: Optional[str]
    delivery_timestamp: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True


class ConversationHistoryResponse(BaseModel):
    """Response model for conversation history"""
    session_id: UUID
    phone_number: str
    total_messages: int
    messages: List[ConversationMessageResponse]
    session_info: Dict[str, Any]
    pagination: Dict[str, Any]


class ConversationStatsResponse(BaseModel):
    """Response model for conversation statistics"""
    session_id: UUID
    total_messages: int
    user_messages: int
    ai_responses: int
    avg_ai_processing_time_ms: float
    conversation_duration_minutes: Optional[float]
    current_stage: str
    is_active: bool


@router.get(
    "/sessions/{session_id}/messages",
    response_model=ConversationHistoryResponse,
    summary="Get Conversation History",
    description="Retrieve complete conversation history for a specific session"
)
async def get_conversation_history(
    session_id: UUID,
    limit: int = Query(50, ge=1, le=500, description="Maximum number of messages to return"),
    offset: int = Query(0, ge=0, description="Number of messages to skip"),
    include_deleted: bool = Query(False, description="Include deleted messages"),
    current_user: dict = Depends(get_current_user)
):
    """Get conversation history for a specific session"""
    try:
        # Get session info
        async with get_db_session() as db:
            session = await db.get(ConversationSession, session_id)
            if not session:
                return create_error_response(
                    error_code=ErrorCodes.NOT_FOUND,
                    message_title="Session Not Found",
                    message_description=f"Conversation session {session_id} not found",
                    status_code=status.HTTP_404_NOT_FOUND
                )
        
        # Get messages
        messages = await ConversationMessageService.get_conversation_history(
            session_id=session_id,
            limit=limit,
            offset=offset,
            include_deleted=include_deleted
        )
        
        # Get conversation stats
        stats = await ConversationMessageService.get_conversation_stats(session_id)
        
        # Build response
        response_data = ConversationHistoryResponse(
            session_id=session_id,
            phone_number=session.phone_number,
            total_messages=stats["total_messages"],
            messages=[ConversationMessageResponse.from_orm(msg) for msg in messages],
            session_info={
                "current_stage": session.current_stage,
                "is_active": session.is_active,
                "is_completed": session.is_completed,
                "created_at": session.created_at,
                "last_activity_at": session.last_activity_at,
                "lead_id": str(session.lead_id) if session.lead_id else None
            },
            pagination={
                "limit": limit,
                "offset": offset,
                "returned_count": len(messages),
                "total_available": stats["total_messages"],
                "has_more": offset + len(messages) < stats["total_messages"]
            }
        )
        
        return create_success_response(
            data=response_data.dict(),
            message_title="Conversation History Retrieved",
            message_description=f"Retrieved {len(messages)} messages for session {session_id}"
        )
        
    except Exception as e:
        logger.error(
            "Failed to get conversation history",
            session_id=str(session_id),
            error=str(e),
            exc_info=True
        )
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Retrieval Failed",
            message_description="Failed to retrieve conversation history",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/phone/{phone_number}/recent",
    response_model=List[ConversationMessageResponse],
    summary="Get Recent Messages by Phone",
    description="Retrieve recent conversation messages for a phone number"
)
async def get_recent_messages_by_phone(
    phone_number: str,
    days: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of messages to return"),
    current_user: dict = Depends(get_current_user)
):
    """Get recent conversation messages for a phone number"""
    try:
        messages = await ConversationMessageService.get_recent_conversations_by_phone(
            phone_number=phone_number,
            days=days,
            limit=limit
        )
        
        response_messages = [ConversationMessageResponse.from_orm(msg) for msg in messages]
        
        return create_success_response(
            data=response_messages,
            message_title="Recent Messages Retrieved",
            message_description=f"Retrieved {len(messages)} recent messages for {phone_number}"
        )
        
    except Exception as e:
        logger.error(
            "Failed to get recent messages by phone",
            phone_number=phone_number,
            error=str(e),
            exc_info=True
        )
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Retrieval Failed",
            message_description="Failed to retrieve recent messages",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/sessions/{session_id}/stats",
    response_model=ConversationStatsResponse,
    summary="Get Conversation Statistics",
    description="Get detailed statistics for a conversation session"
)
async def get_conversation_stats(
    session_id: UUID,
    current_user: dict = Depends(get_current_user)
):
    """Get conversation statistics for a session"""
    try:
        # Get session info
        async with get_db_session() as db:
            session = await db.get(ConversationSession, session_id)
            if not session:
                return create_error_response(
                    error_code=ErrorCodes.NOT_FOUND,
                    message_title="Session Not Found",
                    message_description=f"Conversation session {session_id} not found",
                    status_code=status.HTTP_404_NOT_FOUND
                )
        
        # Get stats
        stats = await ConversationMessageService.get_conversation_stats(session_id)
        
        # Calculate conversation duration
        duration_minutes = None
        if session.created_at and session.last_activity_at:
            duration = session.last_activity_at - session.created_at
            duration_minutes = duration.total_seconds() / 60
        
        response_data = ConversationStatsResponse(
            session_id=session_id,
            total_messages=stats["total_messages"],
            user_messages=stats["user_messages"],
            ai_responses=stats["ai_responses"],
            avg_ai_processing_time_ms=stats["avg_ai_processing_time_ms"],
            conversation_duration_minutes=duration_minutes,
            current_stage=session.current_stage,
            is_active=session.is_active
        )
        
        return create_success_response(
            data=response_data.dict(),
            message_title="Conversation Statistics Retrieved",
            message_description=f"Retrieved statistics for session {session_id}"
        )
        
    except Exception as e:
        logger.error(
            "Failed to get conversation stats",
            session_id=str(session_id),
            error=str(e),
            exc_info=True
        )
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Statistics Failed",
            message_description="Failed to retrieve conversation statistics",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put(
    "/messages/{message_id}/delivery-status",
    summary="Update Message Delivery Status",
    description="Update the delivery status of an AI response message"
)
async def update_message_delivery_status(
    message_id: UUID,
    delivery_status: str = Query(..., regex="^(pending|sent|delivered|failed)$"),
    error_message: Optional[str] = Query(None, description="Error message if delivery failed"),
    current_user: dict = Depends(get_current_user)
):
    """Update delivery status for a message"""
    try:
        success = await ConversationMessageService.update_delivery_status(
            message_id=message_id,
            delivery_status=delivery_status,
            error_message=error_message
        )
        
        if not success:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Message Not Found",
                message_description=f"Message {message_id} not found",
                status_code=status.HTTP_404_NOT_FOUND
            )
        
        return create_success_response(
            data={"message_id": str(message_id), "delivery_status": delivery_status},
            message_title="Delivery Status Updated",
            message_description=f"Updated delivery status to {delivery_status}"
        )
        
    except Exception as e:
        logger.error(
            "Failed to update delivery status",
            message_id=str(message_id),
            error=str(e),
            exc_info=True
        )
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Update Failed",
            message_description="Failed to update delivery status",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
