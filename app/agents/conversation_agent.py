"""
Conversational SMS Chatbot Agent
Handles structured conversation flow for lead engagement, qualification, and Q&A
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import structlog
from langchain_core.messages import AIMessage
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from .base import BaseAgent, AgentState
from .tools.registry import tool_registry
from app.core.database.connection import get_db
from contextlib import asynccontextmanager
from app.models.sales_script import SalesScript
from app.models.pre_qualification_question import PreQualificationQuestion
from app.models.conversation_session import ConversationSession
from app.models.lead import Lead, LeadResponse
from app.services.holiday_availability_service import HolidayAvailabilityService

logger = structlog.get_logger()


@asynccontextmanager
async def get_db_session():
    """Helper to get database session as context manager"""
    async for db in get_db():
        yield db
        break


class ConversationStage:
    """Constants for conversation stages"""
    INITIAL_GREETING = "initial_greeting"
    PREQUALIFICATION = "prequalification"
    DOCUMENT_QA = "document_qa"
    FOLLOWUP = "followup"
    GOODBYE = "goodbye"


class ConversationAgent(BaseAgent):
    """
    Conversational SMS chatbot agent that manages structured conversation flow
    """
    
    def _initialize_tools(self):
        """Initialize conversation-specific tools"""
        tool_names = ["search_documents", "create_lead", "create_communication", "store_memory"]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process conversation state through structured flow"""
        try:
            user_input = state.get("user_input", "")
            context = state.get("context", {})
            phone_number = context.get("sender", "")

            if not phone_number:
                state["error"] = "Phone number not found in context"
                return state

            # Check admin availability first
            async with get_db_session() as db:
                availability_service = HolidayAvailabilityService(db)
                is_available, out_of_reach_message = await availability_service.check_admin_availability()

                if not is_available and out_of_reach_message:
                    print(f"🏖️ Admin unavailable due to holiday - terminating conversation")

                    # Mark any existing session as completed due to unavailability
                    session = await self._get_or_create_session(phone_number, user_input)
                    session.is_completed = True
                    session.completion_reason = "admin_unavailable"
                    session.completed_at = datetime.now(timezone.utc)
                    await self._update_session(session, user_input, out_of_reach_message)

                    # Return the out of reach message and terminate
                    state["response"] = out_of_reach_message
                    state["session_data"] = {
                        "session_id": session.session_id,
                        "stage": "terminated",
                        "completion_reason": "admin_unavailable"
                    }
                    return state

            # Get or create conversation session
            session = await self._get_or_create_session(phone_number, user_input)
            
            # Process based on current stage
            print(f"🔄 Processing stage: {session.current_stage} for phone: {phone_number}")
            if session.current_stage == ConversationStage.INITIAL_GREETING:
                print("📞 Handling initial greeting")
                response = await self._handle_initial_greeting(session, user_input)
            elif session.current_stage == ConversationStage.PREQUALIFICATION:
                response = await self._handle_prequalification(session, user_input)
            elif session.current_stage == ConversationStage.DOCUMENT_QA:
                response = await self._handle_document_qa(session, user_input)
            elif session.current_stage == ConversationStage.FOLLOWUP:
                response = await self._handle_followup(session, user_input)
            elif session.current_stage == ConversationStage.GOODBYE:
                response = await self._handle_goodbye(session, user_input)
            else:
                response = "I'm sorry, there seems to be an issue with our conversation. Let me restart."
                await self._reset_session(session)
            
            # Update session with response
            await self._update_session(session, user_input, response)

            # Get updated session from database to ensure we have the latest state
            async with get_db_session() as db:
                stmt = select(ConversationSession).where(
                    ConversationSession.phone_number == phone_number
                )
                result = await db.execute(stmt)
                updated_session = result.scalar_one_or_none()
                if updated_session:
                    session = updated_session

            # Update state
            state["response"] = response
            state["next_action"] = "end"
            state["messages"] = state.get("messages", []) + [AIMessage(content=response)]
            state["session_data"] = {
                "session_id": session.session_id,
                "stage": session.current_stage,
                "phone_number": phone_number
            }
            
            logger.info(f"Conversation processed - Stage: {session.current_stage}, Phone: {phone_number}")
            return state
            
        except Exception as e:
            logger.error(f"Error in conversation agent: {str(e)}")
            state["error"] = str(e)
            state["response"] = "I apologize, but I encountered an error. Please try again."
            return state
    
    async def _get_or_create_session(self, phone_number: str, user_input: str) -> ConversationSession:
        """Get existing session or create new one"""
        async with get_db_session() as db:
            session_id = f"sms_{phone_number}"
            
            # Try to get existing active session
            stmt = select(ConversationSession).where(
                and_(
                    ConversationSession.phone_number == phone_number,
                    ConversationSession.is_active == True,
                    ConversationSession.is_completed == False
                )
            )
            result = await db.execute(stmt)
            session = result.scalar_one_or_none()
            
            if not session:
                # Create new session
                session = ConversationSession(
                    phone_number=phone_number,
                    session_id=session_id,
                    current_stage=ConversationStage.INITIAL_GREETING,
                    last_message_received=user_input,
                    message_count=1
                )
                db.add(session)
                await db.commit()
                await db.refresh(session)
                logger.info(f"Created new conversation session for {phone_number}")
            
            return session
    
    async def _handle_initial_greeting(self, session: ConversationSession, user_input: str) -> str:
        """Handle initial greeting stage"""
        print(f"📞 Initial greeting - Current stage: {session.current_stage}")

        # Get greeting script
        async with get_db_session() as db:
            stmt = select(SalesScript).where(
                and_(
                    SalesScript.script_title == "Initial greeting",
                    SalesScript.is_active == True,
                    SalesScript.is_deleted == False
                )
            )
            result = await db.execute(stmt)
            script = result.scalar_one_or_none()

        # Update the session object directly (will be committed in _update_session)
        print(f"📞 Moving to prequalification stage")
        session.current_stage = ConversationStage.PREQUALIFICATION
        print(f"📞 Stage updated to: {session.current_stage}")

        if script:
            return script.script_content
        else:
            return "Hi! I'm here to help you explore the franchise opportunity. Let's get started!"
    
    async def _handle_prequalification(self, session: ConversationSession, user_input: str) -> str:
        """Handle prequalification stage"""
        print(f"❓ Prequalification - questions_asked: {session.questions_asked}, current_question_id: {session.current_question_id}")
        # If this is the first message in prequalification, send introduction
        if session.questions_asked == 0:
            # Get qualification introduction script
            async with get_db_session() as db:
                stmt = select(SalesScript).where(
                    and_(
                        SalesScript.script_title == "Qualification Introduction",
                        SalesScript.is_active == True,
                        SalesScript.is_deleted == False
                    )
                )
                result = await db.execute(stmt)
                script = result.scalar_one_or_none()

                intro_message = script.script_content if script else "I'd like to ask you a few questions to better understand your interests."

                # Get first question
                first_question = await self._get_next_question(db, session)
                if first_question:
                    # Update session object directly (will be committed in _update_session)
                    session.current_question_id = first_question.id
                    session.questions_asked = 1
                    return f"{intro_message}\n\n{first_question.question_text}"
                else:
                    # No questions available, move to document Q&A
                    session.current_stage = ConversationStage.DOCUMENT_QA
                    return await self._handle_document_qa_introduction(db, session)
            
        # Process answer to current question
        if session.current_question_id:
            async with get_db_session() as db:
                await self._process_qualification_answer(db, session, user_input)

                # Check if more questions
                next_question = await self._get_next_question(db, session)
                if next_question:
                    # Update session object directly (will be committed in _update_session)
                    session.current_question_id = next_question.id
                    session.questions_asked += 1
                    return next_question.question_text
                else:
                    # All questions answered, evaluate qualification
                    qualification_result = await self._evaluate_qualification(db, session)

                    # Move to document Q&A stage
                    session.current_stage = ConversationStage.DOCUMENT_QA

                    return f"{qualification_result}\n\n{await self._handle_document_qa_introduction(db, session)}"

        return "I didn't understand your response. Could you please try again?"

    async def _handle_document_qa(self, session: ConversationSession, user_input: str) -> str:
        """Handle document Q&A stage"""
        # Check if user wants to end conversation
        if user_input.lower().strip() in ["no", "no thanks", "that's all", "goodbye", "bye", "done", "finished", "end", "stop", "quit"]:
            session.current_stage = ConversationStage.GOODBYE
            return await self._handle_goodbye(session, user_input)

        # Use RAG system to answer questions
        try:
            from docqa.production_integration import ProductionRAGSystem
            rag_system = ProductionRAGSystem()

            # Answer the question using RAG
            result = await rag_system.answer_question(
                question=user_input,
                similarity_threshold=0.1,  # Low threshold for better recall
                top_k=6,
                temperature=0.3
            )

            if result.get('success') and result.get('answer'):
                answer = result['answer']
                # Add follow-up prompt
                return f"{answer}\n\nDo you have any other questions about our franchise opportunity? (Type 'done' when finished)"
            else:
                return "I'm sorry, I couldn't find specific information about that in our materials. Could you try rephrasing your question or ask about something else? (Type 'done' when finished)"

        except Exception as e:
            logger.error(f"Error in document Q&A: {str(e)}")
            return "I'm sorry, I'm having trouble accessing our information right now. Could you try asking your question again? (Type 'done' when finished)"

    async def _handle_followup(self, session: ConversationSession, user_input: str) -> str:
        """Handle followup stage"""
        # Check if user wants to end conversation
        if user_input.lower().strip() in ["no", "no thanks", "that's all", "goodbye", "bye", "done", "finished", "end", "stop", "quit"]:
            session.current_stage = ConversationStage.GOODBYE
            return await self._handle_goodbye(session, user_input)
        else:
            # Treat any other input as a question and handle in document Q&A
            session.current_stage = ConversationStage.DOCUMENT_QA
            return await self._handle_document_qa(session, user_input)

    async def _handle_goodbye(self, session: ConversationSession, user_input: str) -> str:
        """Handle goodbye stage"""
        async with get_db_session() as db:
            # Get goodbye script
            stmt = select(SalesScript).where(
                and_(
                    SalesScript.script_title == "Goodbye",
                    SalesScript.is_active == True,
                    SalesScript.is_deleted == False
                )
            )
            result = await db.execute(stmt)
            script = result.scalar_one_or_none()

            # Mark session as completed
            session.is_completed = True
            session.completion_reason = "goodbye"
            session.completed_at = datetime.now(timezone.utc)
            await db.commit()

            if script:
                return script.script_content
            else:
                return "Thank you for your interest! Have a great day and feel free to reach out if you have any more questions."

    async def _handle_document_qa_introduction(self, db: AsyncSession, session: ConversationSession) -> str:
        """Get document Q&A introduction message"""
        stmt = select(SalesScript).where(
            and_(
                SalesScript.script_title == "Document Q&A Introduction",
                SalesScript.is_active == True,
                SalesScript.is_deleted == False
            )
        )
        result = await db.execute(stmt)
        script = result.scalar_one_or_none()

        if script:
            return script.script_content
        else:
            return "Now I can answer questions about our franchise opportunity based on our brochure. What would you like to know?"

    async def _get_next_question(self, db: AsyncSession, session: ConversationSession) -> Optional[PreQualificationQuestion]:
        """Get the next pre-qualification question"""
        stmt = select(PreQualificationQuestion).where(
            and_(
                PreQualificationQuestion.is_active == True,
                PreQualificationQuestion.is_deleted == False,
                PreQualificationQuestion.order_sequence > session.questions_asked
            )
        ).order_by(PreQualificationQuestion.order_sequence).limit(1)

        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def _process_qualification_answer(self, db: AsyncSession, session: ConversationSession, answer: str):
        """Process and store qualification answer"""
        if not session.current_question_id or not session.lead_id:
            # Create lead if it doesn't exist
            if not session.lead_id:
                # Default lead status ID for "New Lead"
                import uuid
                default_lead_status_id = uuid.UUID("f53c50cf-9374-4d18-98c1-c905215051eb")

                lead = Lead(
                    first_name=f"SMS Lead",
                    last_name=session.phone_number,
                    phone=session.phone_number,
                    lead_status_id=default_lead_status_id
                )
                db.add(lead)
                await db.commit()
                await db.refresh(lead)

                session.lead_id = lead.id
                await db.commit()

        # Store the response
        if session.lead_id and session.current_question_id:
            lead_response = LeadResponse(
                lead_id=session.lead_id,
                question_id=session.current_question_id,
                response_text=answer
            )
            db.add(lead_response)
            session.questions_answered += 1
            await db.commit()

    async def _evaluate_qualification(self, db: AsyncSession, session: ConversationSession) -> str:
        """Evaluate lead qualification based on answers"""
        if not session.lead_id:
            return "Thank you for your responses."

        # Simple qualification logic - can be enhanced
        qualification_threshold = 0.8  # 80%

        # Get all questions and responses
        stmt = select(LeadResponse).where(LeadResponse.lead_id == session.lead_id)
        result = await db.execute(stmt)
        responses = result.scalars().all()

        if len(responses) > 0:
            # Simple scoring: assume all responses are positive for now
            # In real implementation, you'd analyze the actual responses
            score = min(len(responses) / 3.0, 1.0)  # Assume 3 questions for full qualification
            session.qualification_score = f"{score * 100:.1f}%"
            session.is_qualified = score >= qualification_threshold

            # Update lead status
            stmt = select(Lead).where(Lead.id == session.lead_id)
            result = await db.execute(stmt)
            lead = result.scalar_one_or_none()

            if lead:
                lead.qualification_status = "qualified" if session.is_qualified else "unqualified"
                await db.commit()

            if session.is_qualified:
                return f"Great! Based on your responses, you seem like a good fit for this franchise opportunity (Score: {session.qualification_score})."
            else:
                return f"Thank you for your responses. While this particular opportunity might not be the perfect fit (Score: {session.qualification_score}), I can still answer questions about it."

        return "Thank you for your responses."

    async def _update_session(self, session: ConversationSession, user_input: str, bot_response: str):
        """Update session with latest message exchange"""
        async with get_db_session() as db:
            # Merge the session object into the current database session
            merged_session = await db.merge(session)
            merged_session.last_message_received = user_input
            merged_session.last_message_sent = bot_response
            merged_session.message_count += 1
            merged_session.last_activity_at = datetime.now(timezone.utc)
            # Ensure all session fields are updated
            merged_session.current_stage = session.current_stage
            merged_session.questions_asked = session.questions_asked
            merged_session.current_question_id = session.current_question_id
            await db.commit()
            await db.refresh(merged_session)
            # Update the original session object with the committed values
            session.current_stage = merged_session.current_stage
            session.last_message_received = merged_session.last_message_received
            session.last_message_sent = merged_session.last_message_sent
            session.message_count = merged_session.message_count
            session.last_activity_at = merged_session.last_activity_at
            session.questions_asked = merged_session.questions_asked
            session.current_question_id = merged_session.current_question_id

            # Store conversation messages in conversation_logs table if lead_id exists
            if session.lead_id:
                try:
                    from app.repositories.conversation_log_repository import ConversationLogRepository
                    from app.services.conversation_log_service import ConversationLogService

                    conversation_log_repo = ConversationLogRepository(db)
                    conversation_log_service = ConversationLogService(conversation_log_repo)

                    # Store user message in conversation_logs
                    await conversation_log_service.store_user_message(
                        lead_id=session.lead_id,
                        message=user_input
                    )

                    # Store system response in conversation_logs
                    await conversation_log_service.store_system_message(
                        lead_id=session.lead_id,
                        message=bot_response
                    )

                    logger.info(
                        "Stored conversation messages in conversation_logs from conversation agent",
                        lead_id=str(session.lead_id),
                        session_id=session.session_id,
                        user_message_length=len(user_input),
                        system_response_length=len(bot_response)
                    )

                except Exception as log_error:
                    logger.error(
                        "Failed to store conversation messages in conversation_logs from conversation agent",
                        error=str(log_error),
                        lead_id=str(session.lead_id) if session.lead_id else None,
                        session_id=session.session_id,
                        exc_info=True
                    )

    async def _reset_session(self, session: ConversationSession):
        """Reset session to initial state"""
        async with get_db_session() as db:
            session.current_stage = ConversationStage.INITIAL_GREETING
            session.current_question_id = None
            session.questions_asked = 0
            session.questions_answered = 0
            session.qualification_score = None
            session.is_qualified = None
            await db.commit()

    def get_conversation_capabilities(self) -> List[str]:
        """Get list of conversation capabilities"""
        return [
            "Initial greeting and welcome",
            "Pre-qualification questioning",
            "Document-based Q&A using RAG",
            "Lead scoring and qualification",
            "Conversation state management",
            "Graceful conversation termination"
        ]
