"""
Conversation Log Schemas
Pydantic schemas for conversation log API operations
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, Field, validator
from enum import Enum


class SenderType(str, Enum):
    """Enum for message sender types"""
    USER = "user"
    SYSTEM = "system"


class ConversationLogBase(BaseModel):
    """Base schema for conversation log"""
    lead_id: UUID = Field(..., description="Lead ID this conversation belongs to")
    sender: SenderType = Field(..., description="Message sender: 'user' or 'system'")
    message: str = Field(..., min_length=1, max_length=10000, description="Message content")

    @validator('message')
    def validate_message(cls, v):
        """Validate and sanitize message content"""
        if not v or not v.strip():
            raise ValueError("Message cannot be empty")
        # Basic sanitization - remove null bytes and control characters
        sanitized = ''.join(char for char in v if ord(char) >= 32 or char in '\n\r\t')
        return sanitized.strip()


class ConversationLogCreate(ConversationLogBase):
    """Schema for creating a conversation log entry"""
    timestamp: Optional[datetime] = Field(None, description="Message timestamp (defaults to now)")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ConversationLogUpdate(BaseModel):
    """Schema for updating a conversation log entry"""
    message: Optional[str] = Field(None, min_length=1, max_length=10000, description="Updated message content")
    is_active: Optional[bool] = Field(None, description="Whether the entry is active")

    @validator('message')
    def validate_message(cls, v):
        """Validate and sanitize message content"""
        if v is not None:
            if not v or not v.strip():
                raise ValueError("Message cannot be empty")
            # Basic sanitization
            sanitized = ''.join(char for char in v if ord(char) >= 32 or char in '\n\r\t')
            return sanitized.strip()
        return v


class ConversationLogResponse(ConversationLogBase):
    """Schema for conversation log response"""
    id: UUID = Field(..., description="Conversation log entry ID")
    timestamp: datetime = Field(..., description="Message timestamp")
    is_active: bool = Field(..., description="Whether the entry is active")
    is_deleted: bool = Field(..., description="Whether the entry is deleted")
    created_at: datetime = Field(..., description="When the entry was created")
    updated_at: datetime = Field(..., description="When the entry was last updated")
    deleted_at: Optional[datetime] = Field(None, description="When the entry was deleted")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class ConversationLogListResponse(BaseModel):
    """Schema for conversation log list response"""
    items: List[ConversationLogResponse] = Field(..., description="List of conversation log entries")
    total: int = Field(..., description="Total number of entries")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Number of items per page")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_prev: bool = Field(..., description="Whether there are previous pages")

    class Config:
        from_attributes = True


class ConversationLogFilter(BaseModel):
    """Schema for filtering conversation logs"""
    lead_id: Optional[UUID] = Field(None, description="Filter by lead ID")
    sender: Optional[SenderType] = Field(None, description="Filter by sender type")
    start_date: Optional[datetime] = Field(None, description="Filter messages from this date")
    end_date: Optional[datetime] = Field(None, description="Filter messages until this date")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    search: Optional[str] = Field(None, min_length=1, max_length=100, description="Search in message content")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class StoreConversationMessageRequest(BaseModel):
    """Schema for storing conversation message via service"""
    lead_id: UUID = Field(..., description="Lead ID")
    sender: SenderType = Field(..., description="Message sender")
    message: str = Field(..., min_length=1, max_length=10000, description="Message content")
    timestamp: Optional[datetime] = Field(None, description="Message timestamp")

    @validator('message')
    def validate_message(cls, v):
        """Validate and sanitize message content"""
        if not v or not v.strip():
            raise ValueError("Message cannot be empty")
        # Basic sanitization
        sanitized = ''.join(char for char in v if ord(char) >= 32 or char in '\n\r\t')
        return sanitized.strip()

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
