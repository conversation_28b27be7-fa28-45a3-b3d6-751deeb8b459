"""
Webhook Message Handler
Enhanced webhook processing with conversation message storage
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional
from uuid import UUID
import structlog

from app.services.conversation_message_service import ConversationMessageService
from app.models.conversation_session import ConversationSession
from app.core.database.connection import get_db_session

logger = structlog.get_logger()


class WebhookMessageHandler:
    """Handler for processing webhook messages with conversation storage"""

    @staticmethod
    async def process_sms_inbound(
        webhook_data: Dict[str, Any],
        webhook_id: Optional[UUID] = None
    ) -> Dict[str, Any]:
        """
        Process SMS inbound webhook with conversation message storage
        
        Args:
            webhook_data: Webhook payload data
            webhook_id: ID of the stored webhook record
            
        Returns:
            Dict containing processing result and metadata
        """
        try:
            # Extract message details
            mo = webhook_data.get("mo", {})
            user_message = mo.get("message", "")
            sender_phone = mo.get("sender", "")
            webhook_message_id = mo.get("id", "")
            
            if not user_message or not sender_phone:
                logger.warning("Missing required SMS data", webhook_data=webhook_data)
                return {
                    "success": False,
                    "error": "Missing message content or sender information"
                }
            
            # Get or create conversation session
            session = await WebhookMessageHandler._get_or_create_session(sender_phone)
            
            # Store user message
            user_message_record = await ConversationMessageService.store_user_message(
                session_id=session.id,
                phone_number=sender_phone,
                message_content=user_message,
                conversation_stage=session.current_stage,
                webhook_id=webhook_id,
                webhook_message_id=webhook_message_id
            )
            
            logger.info(
                "User message stored from webhook",
                session_id=str(session.id),
                phone_number=sender_phone,
                message_id=str(user_message_record.id),
                sequence=user_message_record.message_sequence
            )
            
            # Process with conversation agent (existing logic)
            from app.agents.conversation_agent import ConversationAgent
            from app.agents.base import AgentConfig, AgentRole
            from langchain_core.messages import HumanMessage
            
            config = AgentConfig(
                role=AgentRole.CONVERSATION,
                name="SMS Conversation Agent",
                description="Handle SMS conversations with context awareness"
            )
            agent = ConversationAgent(config)
            
            # Prepare agent state with session context
            state = {
                "user_input": user_message,
                "messages": [HumanMessage(content=user_message)],
                "session_id": f"sms_{sender_phone}",
                "intent": None,
                "next_action": None,
                "lead_id": str(session.lead_id) if session.lead_id else None,
                "lead_data": None,
                "lead_status": None,
                "document_id": None,
                "document_content": None,
                "search_results": None,
                "meeting_data": None,
                "availability": None,
                "context": {
                    "platform": "sms",
                    "sender": sender_phone,
                    "recipient": mo.get("recipient", ""),
                    "webhook_payload": webhook_data,
                    "session_id": str(session.id),
                    "conversation_stage": session.current_stage,
                    "message_count": session.message_count + 1
                },
                "conversation_history": [],
                "response": None,
                "error": None,
                "metadata": {},
                "current_agent": None,
                "execution_path": [],
                "retry_count": 0,
                "existing_session": session  # Pass the session object to avoid conflicts
            }
            
            # Process with conversation agent
            processing_start = datetime.now(timezone.utc)
            result_state = await agent.process_state(state)
            processing_end = datetime.now(timezone.utc)
            processing_time_ms = int((processing_end - processing_start).total_seconds() * 1000)
            
            # Get AI response
            ai_response = result_state.get("response", "I'm here to help with your franchise questions.")
            
            # Store AI response message
            ai_message_record = await ConversationMessageService.store_ai_response(
                session_id=session.id,
                phone_number=sender_phone,
                message_content=ai_response,
                conversation_stage=result_state.get("session_data", {}).get("stage", session.current_stage),
                ai_model_used="conversation_agent",  # Could be extracted from agent config
                processing_time_ms=processing_time_ms,
                rag_sources=result_state.get("search_results"),
                delivery_status="pending"
            )

            logger.info(
                "AI response stored from webhook processing",
                session_id=str(session.id),
                phone_number=sender_phone,
                ai_message_id=str(ai_message_record.id),
                sequence=ai_message_record.message_sequence,
                processing_time_ms=processing_time_ms
            )

            # Store conversation messages in conversation_logs table
            if session.lead_id:
                try:
                    from app.repositories.conversation_log_repository import ConversationLogRepository
                    from app.services.conversation_log_service import ConversationLogService
                    from app.core.database.connection import get_db

                    async for db in get_db():
                        conversation_log_repo = ConversationLogRepository(db)
                        conversation_log_service = ConversationLogService(conversation_log_repo)

                        # Store user message in conversation_logs
                        await conversation_log_service.store_user_message(
                            lead_id=session.lead_id,
                            message=user_message
                        )

                        # Store system response in conversation_logs
                        await conversation_log_service.store_system_message(
                            lead_id=session.lead_id,
                            message=ai_response
                        )

                        logger.info(
                            "Stored conversation messages in conversation_logs from webhook handler",
                            lead_id=str(session.lead_id),
                            user_message_length=len(user_message),
                            system_response_length=len(ai_response)
                        )
                        break

                except Exception as log_error:
                    logger.error(
                        "Failed to store conversation messages in conversation_logs from webhook handler",
                        error=str(log_error),
                        lead_id=str(session.lead_id) if session.lead_id else None,
                        exc_info=True
                    )
            
            # Update session with latest activity
            await WebhookMessageHandler._update_session_activity(session, user_message, ai_response)
            
            # Create response result
            answer_result = {
                "success": not bool(result_state.get("error")),
                "answer": ai_response,
                "error": result_state.get("error"),
                "metadata": {
                    "conversation_stage": result_state.get("session_data", {}).get("stage"),
                    "session_id": str(session.id),
                    "processing_method": "conversational_chatbot",
                    "webhook_type": "sms_inbound",
                    "user_message_id": str(user_message_record.id),
                    "ai_message_id": str(ai_message_record.id),
                    "processing_time_ms": processing_time_ms,
                    "message_sequence": {
                        "user": user_message_record.message_sequence,
                        "ai": ai_message_record.message_sequence
                    }
                }
            }
            
            return answer_result
            
        except Exception as e:
            logger.error(
                "Failed to process SMS inbound webhook",
                error=str(e),
                webhook_data=webhook_data,
                exc_info=True
            )
            return {
                "success": False,
                "answer": "I apologize, but I encountered an error. Please try again.",
                "error": str(e)
            }

    @staticmethod
    async def _get_or_create_session(phone_number: str) -> ConversationSession:
        """Get existing session or create new one for phone number"""
        async with get_db_session() as db:
            # Try to find existing active session
            from sqlalchemy import select
            result = await db.execute(
                select(ConversationSession).where(
                    ConversationSession.phone_number == phone_number,
                    ConversationSession.is_active == True,
                    ConversationSession.is_completed == False
                ).order_by(ConversationSession.created_at.desc())
            )
            session = result.scalars().first()
            
            if session:
                logger.info("Found existing session", session_id=str(session.id), phone_number=phone_number)
                return session
            
            # Create new session
            session_id = f"sms_{phone_number}"
            new_session = ConversationSession(
                phone_number=phone_number,
                session_id=session_id,
                current_stage="initial_greeting",
                is_active=True,
                is_completed=False,
                created_at=datetime.now(timezone.utc)
            )
            
            db.add(new_session)
            await db.commit()
            await db.refresh(new_session)
            
            logger.info("Created new session", session_id=str(new_session.id), phone_number=phone_number)
            return new_session

    @staticmethod
    async def _update_session_activity(
        session: ConversationSession,
        user_message: str,
        ai_response: str
    ):
        """Update session with latest activity"""
        async with get_db_session() as db:
            merged_session = await db.merge(session)
            merged_session.last_message_received = user_message
            merged_session.last_message_sent = ai_response
            merged_session.message_count += 2  # User message + AI response
            merged_session.last_activity_at = datetime.now(timezone.utc)
            await db.commit()

    @staticmethod
    async def mark_message_delivered(
        message_id: UUID,
        delivery_status: str = "delivered",
        error_message: Optional[str] = None
    ) -> bool:
        """
        Mark an AI response message as delivered
        
        Args:
            message_id: ID of the message to update
            delivery_status: New delivery status
            error_message: Error message if delivery failed
            
        Returns:
            bool: True if update was successful
        """
        return await ConversationMessageService.update_delivery_status(
            message_id=message_id,
            delivery_status=delivery_status,
            error_message=error_message
        )
