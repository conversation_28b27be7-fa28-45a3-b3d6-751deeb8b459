"""
Conversation Log Service
Business logic for conversation log management
"""

from datetime import datetime
from typing import Optional, List, Tuple, Dict, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from app.repositories.conversation_log_repository import ConversationLogRepository
from app.models.conversation_log import ConversationLog
from app.schemas.conversation_log import (
    ConversationLogCreate, 
    ConversationLogUpdate, 
    ConversationLogFilter,
    SenderType,
    StoreConversationMessageRequest
)
from app.core.logging import logger
import html
import re


class ConversationLogService:
    """Service for conversation log business logic."""

    def __init__(self, repository: ConversationLogRepository):
        self.repository = repository

    async def store_conversation_message(
        self, 
        lead_id: UUID, 
        sender: str, 
        message: str,
        timestamp: Optional[datetime] = None
    ) -> ConversationLog:
        """
        Store a conversation message between the system AI and user.
        
        This is the main function requested by the user for storing messages
        in the conversation_logs table.
        
        Args:
            lead_id: UUID of the lead this conversation belongs to
            sender: Message sender ('user' or 'system')
            message: The message content
            timestamp: Optional timestamp (defaults to now)
            
        Returns:
            ConversationLog: The stored conversation log entry
            
        Raises:
            ValueError: If validation fails
            Exception: If database operation fails
        """
        try:
            # Validate sender
            if sender not in ['user', 'system']:
                raise ValueError(f"Invalid sender '{sender}'. Must be 'user' or 'system'")
            
            # Sanitize message content
            sanitized_message = self._sanitize_message(message)
            
            # Create conversation log data
            log_data = ConversationLogCreate(
                lead_id=lead_id,
                sender=SenderType(sender),
                message=sanitized_message,
                timestamp=timestamp
            )
            
            # Store in database
            conversation_log = await self.repository.create(log_data)
            
            logger.info(
                "Stored conversation message",
                lead_id=str(lead_id),
                sender=sender,
                message_id=str(conversation_log.id),
                message_length=len(sanitized_message)
            )
            
            return conversation_log
            
        except ValueError as e:
            logger.error(f"Validation error storing conversation message: {e}")
            raise
        except Exception as e:
            logger.error(
                f"Error storing conversation message: {e}",
                lead_id=str(lead_id),
                sender=sender
            )
            raise

    async def store_user_message(
        self, 
        lead_id: UUID, 
        message: str,
        timestamp: Optional[datetime] = None
    ) -> ConversationLog:
        """
        Store a user message.
        
        Args:
            lead_id: UUID of the lead
            message: User message content
            timestamp: Optional timestamp
            
        Returns:
            ConversationLog: The stored conversation log entry
        """
        return await self.store_conversation_message(
            lead_id=lead_id,
            sender='user',
            message=message,
            timestamp=timestamp
        )

    async def store_system_message(
        self, 
        lead_id: UUID, 
        message: str,
        timestamp: Optional[datetime] = None
    ) -> ConversationLog:
        """
        Store a system/AI message.
        
        Args:
            lead_id: UUID of the lead
            message: System message content
            timestamp: Optional timestamp
            
        Returns:
            ConversationLog: The stored conversation log entry
        """
        return await self.store_conversation_message(
            lead_id=lead_id,
            sender='system',
            message=message,
            timestamp=timestamp
        )

    async def get_conversation_history(
        self, 
        lead_id: UUID, 
        limit: int = 50, 
        offset: int = 0
    ) -> List[ConversationLog]:
        """
        Get conversation history for a lead.
        
        Args:
            lead_id: Lead ID
            limit: Maximum number of messages
            offset: Number of messages to skip
            
        Returns:
            List of ConversationLog entries
        """
        try:
            return await self.repository.get_by_lead_id(
                lead_id=lead_id,
                limit=limit,
                offset=offset,
                include_deleted=False
            )
        except Exception as e:
            logger.error(f"Error getting conversation history for lead {lead_id}: {e}")
            raise

    async def list_conversations(
        self, 
        filters: ConversationLogFilter,
        limit: int = 50,
        offset: int = 0,
        order_by: str = "timestamp",
        order_direction: str = "desc"
    ) -> Tuple[List[ConversationLog], int]:
        """
        List conversations with filters and pagination.
        
        Args:
            filters: Filter criteria
            limit: Maximum number of entries
            offset: Number of entries to skip
            order_by: Field to order by
            order_direction: Order direction
            
        Returns:
            Tuple of (conversation logs, total count)
        """
        try:
            return await self.repository.list_with_filters(
                filters=filters,
                limit=limit,
                offset=offset,
                order_by=order_by,
                order_direction=order_direction
            )
        except Exception as e:
            logger.error(f"Error listing conversations: {e}")
            raise

    async def get_by_id(self, conversation_log_id: UUID) -> Optional[ConversationLog]:
        """
        Get a conversation log entry by ID.
        
        Args:
            conversation_log_id: Conversation log entry ID
            
        Returns:
            ConversationLog or None
        """
        try:
            return await self.repository.get_by_id(conversation_log_id)
        except Exception as e:
            logger.error(f"Error getting conversation log {conversation_log_id}: {e}")
            raise

    async def update_message(
        self, 
        conversation_log_id: UUID, 
        update_data: ConversationLogUpdate
    ) -> Optional[ConversationLog]:
        """
        Update a conversation log entry.
        
        Args:
            conversation_log_id: Conversation log entry ID
            update_data: Update data
            
        Returns:
            Updated ConversationLog or None
        """
        try:
            # Sanitize message if provided
            if update_data.message:
                update_data.message = self._sanitize_message(update_data.message)
            
            return await self.repository.update(conversation_log_id, update_data)
        except Exception as e:
            logger.error(f"Error updating conversation log {conversation_log_id}: {e}")
            raise

    async def delete_message(self, conversation_log_id: UUID) -> bool:
        """
        Soft delete a conversation log entry.
        
        Args:
            conversation_log_id: Conversation log entry ID
            
        Returns:
            bool: True if deleted successfully
        """
        try:
            return await self.repository.soft_delete(conversation_log_id)
        except Exception as e:
            logger.error(f"Error deleting conversation log {conversation_log_id}: {e}")
            raise

    async def get_conversation_stats(self, lead_id: UUID) -> Dict[str, Any]:
        """
        Get conversation statistics for a lead.
        
        Args:
            lead_id: Lead ID
            
        Returns:
            Dict with conversation statistics
        """
        try:
            # Get all messages for the lead
            messages = await self.repository.get_by_lead_id(
                lead_id=lead_id,
                limit=1000,  # Large limit to get all messages
                offset=0,
                include_deleted=False
            )
            
            user_messages = [m for m in messages if m.sender == 'user']
            system_messages = [m for m in messages if m.sender == 'system']
            
            stats = {
                'total_messages': len(messages),
                'user_messages': len(user_messages),
                'system_messages': len(system_messages),
                'first_message_at': messages[-1].timestamp if messages else None,
                'last_message_at': messages[0].timestamp if messages else None,
                'avg_message_length': sum(len(m.message) for m in messages) / len(messages) if messages else 0
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting conversation stats for lead {lead_id}: {e}")
            raise

    def _sanitize_message(self, message: str) -> str:
        """
        Sanitize message content to prevent harmful characters.
        
        Args:
            message: Raw message content
            
        Returns:
            str: Sanitized message content
        """
        if not message:
            return ""
        
        # HTML escape to prevent XSS
        sanitized = html.escape(message)
        
        # Remove null bytes and other control characters except newlines, tabs, carriage returns
        sanitized = ''.join(char for char in sanitized if ord(char) >= 32 or char in '\n\r\t')
        
        # Remove excessive whitespace
        sanitized = re.sub(r'\s+', ' ', sanitized).strip()
        
        # Limit length to prevent abuse
        if len(sanitized) > 10000:
            sanitized = sanitized[:10000] + "..."
        
        return sanitized
