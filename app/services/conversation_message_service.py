"""
Conversation Message Service
Service layer for managing SMS conversation message storage and retrieval
"""

from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID
import structlog
from sqlalchemy import desc, asc, and_, func
from sqlalchemy.orm import selectinload

from app.core.database.connection import get_db_session
from app.models.conversation_message import ConversationMessage
from app.models.conversation_session import ConversationSession

logger = structlog.get_logger()


class ConversationMessageService:
    """Service for managing conversation messages"""

    @staticmethod
    async def store_user_message(
        session_id: UUID,
        phone_number: str,
        message_content: str,
        conversation_stage: Optional[str] = None,
        webhook_id: Optional[UUID] = None,
        webhook_message_id: Optional[str] = None
    ) -> ConversationMessage:
        """
        Store a user input message
        
        Args:
            session_id: Conversation session ID
            phone_number: User's phone number
            message_content: The user's message content
            conversation_stage: Current conversation stage
            webhook_id: Associated webhook ID
            webhook_message_id: External webhook message ID
            
        Returns:
            ConversationMessage: The stored message record
        """
        try:
            async with get_db_session() as db:
                # Get next sequence number for this session
                sequence = await ConversationMessageService._get_next_sequence(db, session_id)
                
                # Create user message record
                message = ConversationMessage(
                    session_id=session_id,
                    message_content=message_content,
                    message_type="user_input",
                    phone_number=phone_number,
                    message_sequence=sequence,
                    conversation_stage=conversation_stage,
                    webhook_id=webhook_id,
                    webhook_message_id=webhook_message_id,
                    created_at=datetime.now(timezone.utc)
                )
                
                db.add(message)
                await db.commit()
                await db.refresh(message)
                
                logger.info(
                    "User message stored",
                    session_id=str(session_id),
                    phone_number=phone_number,
                    sequence=sequence,
                    message_length=len(message_content)
                )
                
                return message
                
        except Exception as e:
            logger.error(
                "Failed to store user message",
                session_id=str(session_id),
                phone_number=phone_number,
                error=str(e),
                exc_info=True
            )
            raise

    @staticmethod
    async def store_ai_response(
        session_id: UUID,
        phone_number: str,
        message_content: str,
        conversation_stage: Optional[str] = None,
        ai_model_used: Optional[str] = None,
        processing_time_ms: Optional[int] = None,
        rag_sources: Optional[Dict[str, Any]] = None,
        delivery_status: str = "pending"
    ) -> ConversationMessage:
        """
        Store an AI response message
        
        Args:
            session_id: Conversation session ID
            phone_number: User's phone number
            message_content: The AI's response content
            conversation_stage: Current conversation stage
            ai_model_used: AI model used for generation
            processing_time_ms: Time taken to generate response
            rag_sources: RAG sources and metadata used
            delivery_status: Initial delivery status
            
        Returns:
            ConversationMessage: The stored message record
        """
        try:
            async with get_db_session() as db:
                # Get next sequence number for this session
                sequence = await ConversationMessageService._get_next_sequence(db, session_id)
                
                # Create AI response record
                message = ConversationMessage(
                    session_id=session_id,
                    message_content=message_content,
                    message_type="ai_response",
                    phone_number=phone_number,
                    message_sequence=sequence,
                    conversation_stage=conversation_stage,
                    ai_model_used=ai_model_used,
                    ai_processing_time_ms=processing_time_ms,
                    rag_sources_used=rag_sources,
                    delivery_status=delivery_status,
                    created_at=datetime.now(timezone.utc)
                )
                
                db.add(message)
                await db.commit()
                await db.refresh(message)
                
                logger.info(
                    "AI response stored",
                    session_id=str(session_id),
                    phone_number=phone_number,
                    sequence=sequence,
                    ai_model=ai_model_used,
                    processing_time_ms=processing_time_ms,
                    message_length=len(message_content)
                )
                
                return message
                
        except Exception as e:
            logger.error(
                "Failed to store AI response",
                session_id=str(session_id),
                phone_number=phone_number,
                error=str(e),
                exc_info=True
            )
            raise

    @staticmethod
    async def get_conversation_history(
        session_id: UUID,
        limit: Optional[int] = None,
        offset: int = 0,
        include_deleted: bool = False
    ) -> List[ConversationMessage]:
        """
        Get conversation history for a session
        
        Args:
            session_id: Conversation session ID
            limit: Maximum number of messages to return
            offset: Number of messages to skip
            include_deleted: Whether to include deleted messages
            
        Returns:
            List[ConversationMessage]: List of messages ordered by sequence
        """
        try:
            async with get_db_session() as db:
                from sqlalchemy import select
                query = select(ConversationMessage).where(
                    ConversationMessage.session_id == session_id
                )
                
                if not include_deleted:
                    query = query.where(ConversationMessage.is_deleted == False)
                
                query = query.order_by(asc(ConversationMessage.message_sequence))
                
                if offset > 0:
                    query = query.offset(offset)
                
                if limit:
                    query = query.limit(limit)
                
                result = await db.execute(query)
                messages = result.scalars().all()
                
                logger.info(
                    "Retrieved conversation history",
                    session_id=str(session_id),
                    message_count=len(messages),
                    limit=limit,
                    offset=offset
                )
                
                return messages
                
        except Exception as e:
            logger.error(
                "Failed to retrieve conversation history",
                session_id=str(session_id),
                error=str(e),
                exc_info=True
            )
            raise

    @staticmethod
    async def get_recent_conversations_by_phone(
        phone_number: str,
        days: int = 30,
        limit: int = 100
    ) -> List[ConversationMessage]:
        """
        Get recent conversation messages for a phone number
        
        Args:
            phone_number: Phone number to search for
            days: Number of days to look back
            limit: Maximum number of messages to return
            
        Returns:
            List[ConversationMessage]: Recent messages ordered by creation time
        """
        try:
            async with get_db_session() as db:
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
                
                query = select(ConversationMessage).where(
                    and_(
                        ConversationMessage.phone_number == phone_number,
                        ConversationMessage.created_at >= cutoff_date,
                        ConversationMessage.is_deleted == False
                    )
                ).order_by(desc(ConversationMessage.created_at)).limit(limit)
                
                result = await db.execute(query)
                messages = result.scalars().all()
                
                logger.info(
                    "Retrieved recent conversations by phone",
                    phone_number=phone_number,
                    days=days,
                    message_count=len(messages)
                )
                
                return messages
                
        except Exception as e:
            logger.error(
                "Failed to retrieve recent conversations",
                phone_number=phone_number,
                error=str(e),
                exc_info=True
            )
            raise

    @staticmethod
    async def update_delivery_status(
        message_id: UUID,
        delivery_status: str,
        error_message: Optional[str] = None
    ) -> bool:
        """
        Update delivery status for a message
        
        Args:
            message_id: Message ID to update
            delivery_status: New delivery status
            error_message: Error message if delivery failed
            
        Returns:
            bool: True if update was successful
        """
        try:
            async with get_db_session() as db:
                message = await db.get(ConversationMessage, message_id)
                if not message:
                    logger.warning("Message not found for delivery update", message_id=str(message_id))
                    return False
                
                message.delivery_status = delivery_status
                message.delivery_timestamp = datetime.now(timezone.utc)
                if error_message:
                    message.error_message = error_message
                
                await db.commit()
                
                logger.info(
                    "Updated message delivery status",
                    message_id=str(message_id),
                    delivery_status=delivery_status,
                    has_error=bool(error_message)
                )
                
                return True
                
        except Exception as e:
            logger.error(
                "Failed to update delivery status",
                message_id=str(message_id),
                error=str(e),
                exc_info=True
            )
            return False

    @staticmethod
    async def _get_next_sequence(db, session_id: UUID) -> int:
        """Get the next sequence number for a session"""
        from sqlalchemy import select
        result = await db.execute(
            select(func.coalesce(func.max(ConversationMessage.message_sequence), 0))
            .where(ConversationMessage.session_id == session_id)
        )
        max_sequence = result.scalar() or 0
        return max_sequence + 1

    @staticmethod
    async def get_conversation_stats(session_id: UUID) -> Dict[str, Any]:
        """
        Get conversation statistics for a session
        
        Args:
            session_id: Conversation session ID
            
        Returns:
            Dict with conversation statistics
        """
        try:
            async with get_db_session() as db:
                # Get message counts by type
                user_count = await db.execute(
                    select(func.count(ConversationMessage.id))
                    .where(
                        and_(
                            ConversationMessage.session_id == session_id,
                            ConversationMessage.message_type == "user_input",
                            ConversationMessage.is_deleted == False
                        )
                    )
                )
                
                ai_count = await db.execute(
                    select(func.count(ConversationMessage.id))
                    .where(
                        and_(
                            ConversationMessage.session_id == session_id,
                            ConversationMessage.message_type == "ai_response",
                            ConversationMessage.is_deleted == False
                        )
                    )
                )
                
                # Get average AI processing time
                avg_processing_time = await db.execute(
                    select(func.avg(ConversationMessage.ai_processing_time_ms))
                    .where(
                        and_(
                            ConversationMessage.session_id == session_id,
                            ConversationMessage.message_type == "ai_response",
                            ConversationMessage.ai_processing_time_ms.isnot(None)
                        )
                    )
                )
                
                return {
                    "total_messages": user_count.scalar() + ai_count.scalar(),
                    "user_messages": user_count.scalar(),
                    "ai_responses": ai_count.scalar(),
                    "avg_ai_processing_time_ms": avg_processing_time.scalar() or 0
                }
                
        except Exception as e:
            logger.error(
                "Failed to get conversation stats",
                session_id=str(session_id),
                error=str(e),
                exc_info=True
            )
            return {
                "total_messages": 0,
                "user_messages": 0,
                "ai_responses": 0,
                "avg_ai_processing_time_ms": 0
            }
