stages:
  - deploy

variables:
  PROJECT: "growthhive-ai"
  TECHNOLOGY: "python"
  DEPLOY_PATH: "$PROJECT"
  IP_ADDRESS: "***********"
  USER: "ubuntu"
  CONT_PORT: "8000"

deploy_master:
  stage: deploy
  environment:
    name: $CI_COMMIT_REF_NAME
  variables:
    CONT_PORT: "8000"
    DEPLOY_PATH: $PROJECT
    IP_ADDRESS: "***********"
    USER: "ubuntu"
    SSH_KEY_NAME: "SSH_PRIVATE_KEY"
 
  script:
    - echo "Setting up SSH access"
    - mkdir -p ~/.ssh
    - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
    - echo "Deploying project to EC2"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "sudo rm -rf ${DEPLOY_PATH}"
    - scp -o StrictHostKeyChecking=no -r . ${USER}@${IP_ADDRESS}:${DEPLOY_PATH}
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cp /home/<USER>/env/.env ${DEPLOY_PATH}/.env"
    - echo "Running docker-compose up"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "
        cd ${DEPLOY_PATH} &&
        docker compose up -d --build --force-recreate
      "
    - echo "Cleaning up Docker resources"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "
        docker system prune -af &&
        docker volume prune -f &&
        docker builder prune -af
      "
  only:
    - staging
