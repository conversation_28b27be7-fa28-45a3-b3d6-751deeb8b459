-- Fix for hardcoded UUID issue in lead creation
-- Date: 22-07-2025
-- Issue: Code was using hardcoded UUID f53c50cf-9374-4d18-98c1-c905215051eb for lead status
-- Solution: Updated code to query for lead status by name instead of using hardcoded UUID

-- Verify that the "New Lead" status exists
SELECT id, name, colour, is_active 
FROM lead_statuses 
WHERE name = 'New Lead' 
AND is_active = true 
AND is_deleted = false;

-- If the status doesn't exist, create it
INSERT INTO lead_statuses (id, name, colour, is_active, is_deleted, created_at, updated_at)
SELECT 
    'f53c50cf-9374-4d18-98c1-c905215051eb'::uuid,
    'New Lead',
    '#8B00FF',
    true,
    false,
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM lead_statuses 
    WHERE name = 'New Lead' 
    AND is_active = true 
    AND is_deleted = false
);

-- Verify the fix worked
SELECT 
    'Lead status verification' as check_type,
    COUNT(*) as status_count,
    STRING_AGG(name, ', ') as status_names
FROM lead_statuses 
WHERE name = 'New Lead' 
AND is_active = true 
AND is_deleted = false; 