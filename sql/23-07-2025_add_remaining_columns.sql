-- Add remaining required columns to conversation_messages table
-- Date: 23-07-2025

-- Add all the missing columns
ALTER TABLE conversation_messages 
ADD COLUMN IF NOT EXISTS message_content TEXT,
ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS ai_processing_time_ms INTEGER,
ADD COLUMN IF NOT EXISTS ai_model_used VARCHAR(50),
ADD COLUMN IF NOT EXISTS conversation_stage VARCHAR(50),
ADD COLUMN IF NOT EXISTS webhook_id UUID,
ADD COLUMN IF NOT EXISTS webhook_message_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS delivery_status VARCHAR(20),
ADD COLUMN IF NOT EXISTS delivery_timestamp TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS error_message TEXT,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Update existing records to populate new columns
UPDATE conversation_messages 
SET 
    message_content = message_text,
    phone_number = 'unknown',
    is_active = TRUE,
    is_deleted = FALSE,
    updated_at = created_at
WHERE message_content IS NULL;

-- Make required columns NOT NULL
ALTER TABLE conversation_messages ALTER COLUMN message_content SET NOT NULL;
ALTER TABLE conversation_messages ALTER COLUMN phone_number SET NOT NULL;
ALTER TABLE conversation_messages ALTER COLUMN is_active SET NOT NULL;
ALTER TABLE conversation_messages ALTER COLUMN is_deleted SET NOT NULL;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversation_messages_phone_number ON conversation_messages(phone_number);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_conversation_stage ON conversation_messages(conversation_stage);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_delivery_status ON conversation_messages(delivery_status);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_is_active ON conversation_messages(is_active);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_is_deleted ON conversation_messages(is_deleted);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_webhook_message_id ON conversation_messages(webhook_message_id); 