-- Fix conversation_messages table schema to match the model
-- Date: 23-07-2025

-- Add missing columns to conversation_messages table
ALTER TABLE conversation_messages 
ADD COLUMN IF NOT EXISTS session_id UUID REFERENCES conversation_sessions(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS message_content TEXT,
ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS message_sequence INTEGER,
ADD COLUMN IF NOT EXISTS ai_processing_time_ms INTEGER,
ADD COLUMN IF NOT EXISTS ai_model_used VARCHAR(50),
ADD COLUMN IF NOT EXISTS rag_sources_used JSONB,
ADD COLUMN IF NOT EXISTS conversation_stage VARCHAR(50),
ADD COLUMN IF NOT EXISTS webhook_id UUID REFERENCES webhooks(id),
ADD COLUMN IF NOT EXISTS webhook_message_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS delivery_status VARCHAR(20),
ADD COLUMN IF NOT EXISTS delivery_timestamp TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS error_message TEXT,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversation_messages_session_id ON conversation_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_phone_number ON conversation_messages(phone_number);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_message_type ON conversation_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_conversation_stage ON conversation_messages(conversation_stage);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_delivery_status ON conversation_messages(delivery_status);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_is_active ON conversation_messages(is_active);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_is_deleted ON conversation_messages(is_deleted);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at ON conversation_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_webhook_message_id ON conversation_messages(webhook_message_id);

-- Create composite indexes
CREATE INDEX IF NOT EXISTS idx_conversation_messages_session_sequence ON conversation_messages(session_id, message_sequence);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_phone_created ON conversation_messages(phone_number, created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_type_stage ON conversation_messages(message_type, conversation_stage);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_delivery ON conversation_messages(delivery_status, delivery_timestamp);

-- Update existing data to populate new columns
UPDATE conversation_messages 
SET 
    session_id = conversation_state_id,
    message_content = message_text,
    phone_number = 'unknown',
    message_sequence = 1,
    is_active = TRUE,
    is_deleted = FALSE,
    updated_at = created_at
WHERE session_id IS NULL;

-- Make session_id NOT NULL after populating data
ALTER TABLE conversation_messages ALTER COLUMN session_id SET NOT NULL;
ALTER TABLE conversation_messages ALTER COLUMN message_content SET NOT NULL;
ALTER TABLE conversation_messages ALTER COLUMN phone_number SET NOT NULL;
ALTER TABLE conversation_messages ALTER COLUMN message_sequence SET NOT NULL;
ALTER TABLE conversation_messages ALTER COLUMN message_type SET NOT NULL;
ALTER TABLE conversation_messages ALTER COLUMN is_active SET NOT NULL;
ALTER TABLE conversation_messages ALTER COLUMN is_deleted SET NOT NULL;

-- Drop old columns if they exist and are no longer needed
-- ALTER TABLE conversation_messages DROP COLUMN IF EXISTS conversation_state_id;
-- ALTER TABLE conversation_messages DROP COLUMN IF EXISTS message_text;

COMMENT ON TABLE conversation_messages IS 'Individual conversation message model for storing SMS chat history';
COMMENT ON COLUMN conversation_messages.session_id IS 'Reference to the conversation session';
COMMENT ON COLUMN conversation_messages.message_content IS 'The actual message content (user input or AI response)';
COMMENT ON COLUMN conversation_messages.message_type IS 'Type of message: user_input or ai_response';
COMMENT ON COLUMN conversation_messages.phone_number IS 'Phone number for quick filtering (denormalized for performance)';
COMMENT ON COLUMN conversation_messages.message_sequence IS 'Sequential number of message within the session (1, 2, 3...)';
COMMENT ON COLUMN conversation_messages.ai_processing_time_ms IS 'Time taken to generate AI response in milliseconds';
COMMENT ON COLUMN conversation_messages.ai_model_used IS 'AI model used for response generation (e.g., gpt-4, claude-3)';
COMMENT ON COLUMN conversation_messages.rag_sources_used IS 'RAG sources and metadata used for AI response generation';
COMMENT ON COLUMN conversation_messages.conversation_stage IS 'Conversation stage when message was sent (greeting, prequalification, etc.)';
COMMENT ON COLUMN conversation_messages.webhook_id IS 'Reference to the webhook that triggered this message (for user inputs)';
COMMENT ON COLUMN conversation_messages.webhook_message_id IS 'External message ID from webhook provider (Kudosity)';
COMMENT ON COLUMN conversation_messages.delivery_status IS 'Delivery status for AI responses: pending, sent, delivered, failed';
COMMENT ON COLUMN conversation_messages.delivery_timestamp IS 'When the message was actually delivered (for AI responses)';
COMMENT ON COLUMN conversation_messages.error_message IS 'Error message if delivery failed';
COMMENT ON COLUMN conversation_messages.is_active IS 'Whether this message record is active';
COMMENT ON COLUMN conversation_messages.is_deleted IS 'Whether this message record is deleted';
COMMENT ON COLUMN conversation_messages.created_at IS 'When this message was created/received';
COMMENT ON COLUMN conversation_messages.updated_at IS 'When this message record was last updated';
COMMENT ON COLUMN conversation_messages.deleted_at IS 'When this message record was deleted'; 