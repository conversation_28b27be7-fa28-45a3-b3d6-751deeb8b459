-- Add missing message_sequence column to conversation_messages table
-- Date: 23-07-2025

-- Add the missing column
ALTER TABLE conversation_messages 
ADD COLUMN IF NOT EXISTS message_sequence INTEGER DEFAULT 1;

-- Update existing records to have sequence 1
UPDATE conversation_messages 
SET message_sequence = 1 
WHERE message_sequence IS NULL;

-- Make it NOT NULL
ALTER TABLE conversation_messages ALTER COLUMN message_sequence SET NOT NULL;

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_conversation_messages_sequence ON conversation_messages(message_sequence); 