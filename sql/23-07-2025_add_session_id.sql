-- Add session_id column to conversation_messages table
-- Date: 23-07-2025

-- Add the session_id column
ALTER TABLE conversation_messages 
ADD COLUMN IF NOT EXISTS session_id UUID;

-- Update existing records to use conversation_state_id as session_id
UPDATE conversation_messages 
SET session_id = conversation_state_id 
WHERE session_id IS NULL;

-- Make it NOT NULL after populating
ALTER TABLE conversation_messages ALTER COLUMN session_id SET NOT NULL;

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_conversation_messages_session_id ON conversation_messages(session_id); 