#!/usr/bin/env python3
"""
Test script for SMS conversation storage functionality
Tests the complete conversation storage and retrieval system
"""

import asyncio
import requests
import json
from datetime import datetime
from uuid import uuid4

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_PHONE = "+1234567890"
TEST_MESSAGES = [
    "Hello, I'm interested in your franchise opportunity",
    "What are the initial investment requirements?",
    "Do you provide training?",
    "What kind of support do you offer?",
    "How long does the approval process take?",
    "Thank you for the information"
]


async def test_webhook_conversation_storage():
    """Test SMS webhook processing with conversation storage"""
    print("🧪 Testing SMS Webhook Conversation Storage")
    print("=" * 60)
    
    session_id = None
    message_ids = []
    
    for i, message in enumerate(TEST_MESSAGES, 1):
        print(f"\n--- Test Message {i} ---")
        print(f"📱 Sending: {message}")
        
        # Prepare webhook payload (Kudosity format)
        payload = {
            "event_type": "SMS_INBOUND",
            "timestamp": datetime.now().isoformat() + "Z",
            "mo": {
                "type": "SMS",
                "id": f"test_msg_{i}_{int(datetime.now().timestamp())}",
                "message": message,
                "recipient": "1234567890",
                "sender": TEST_PHONE.replace("+", ""),
                "routed_via": "1234567890"
            }
        }
        
        try:
            # Send webhook
            response = requests.post(
                f"{BASE_URL}/api/webhooks/webhooks/kudosity",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Webhook processed successfully")
                print(f"🤖 AI Response: {result.get('data', {}).get('answer', 'No response')[:100]}...")
                
                # Extract session info from metadata
                metadata = result.get('data', {}).get('metadata', {})
                if not session_id and metadata.get('session_id'):
                    session_id = metadata.get('session_id')
                    print(f"📝 Session ID: {session_id}")
                
                # Store message IDs for later testing
                if metadata.get('user_message_id'):
                    message_ids.append(metadata['user_message_id'])
                if metadata.get('ai_message_id'):
                    message_ids.append(metadata['ai_message_id'])
                    
            else:
                print(f"❌ Webhook failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error sending webhook: {e}")
        
        # Small delay between messages
        await asyncio.sleep(1)
    
    return session_id, message_ids


async def test_conversation_history_api(session_id: str):
    """Test conversation history API endpoints"""
    if not session_id:
        print("❌ No session ID available for history testing")
        return
    
    print(f"\n🔍 Testing Conversation History API")
    print("=" * 60)
    
    # Test 1: Get conversation history
    print("\n--- Test 1: Get Conversation History ---")
    try:
        # Note: In production, you'd need authentication headers
        response = requests.get(
            f"{BASE_URL}/api/v1/conversations/sessions/{session_id}/messages",
            params={"limit": 20, "offset": 0}
        )
        
        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            messages = data.get('messages', [])
            
            print(f"✅ Retrieved {len(messages)} messages")
            print(f"📊 Total messages in session: {data.get('total_messages', 0)}")
            print(f"📱 Phone number: {data.get('phone_number', 'Unknown')}")
            
            # Display conversation flow
            print("\n📝 Conversation Flow:")
            for msg in messages:
                msg_type = "👤 User" if msg['message_type'] == 'user_input' else "🤖 AI"
                content = msg['message_content'][:80] + "..." if len(msg['message_content']) > 80 else msg['message_content']
                print(f"  {msg['message_sequence']:2d}. {msg_type}: {content}")
                
        else:
            print(f"❌ History API failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing history API: {e}")
    
    # Test 2: Get conversation statistics
    print("\n--- Test 2: Get Conversation Statistics ---")
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/conversations/sessions/{session_id}/stats"
        )
        
        if response.status_code == 200:
            result = response.json()
            stats = result.get('data', {})
            
            print(f"✅ Retrieved conversation statistics")
            print(f"📊 Total messages: {stats.get('total_messages', 0)}")
            print(f"👤 User messages: {stats.get('user_messages', 0)}")
            print(f"🤖 AI responses: {stats.get('ai_responses', 0)}")
            print(f"⏱️  Avg AI processing time: {stats.get('avg_ai_processing_time_ms', 0):.1f}ms")
            print(f"🕐 Conversation duration: {stats.get('conversation_duration_minutes', 0):.1f} minutes")
            print(f"📍 Current stage: {stats.get('current_stage', 'Unknown')}")
            print(f"🟢 Is active: {stats.get('is_active', False)}")
            
        else:
            print(f"❌ Stats API failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing stats API: {e}")
    
    # Test 3: Get recent messages by phone
    print("\n--- Test 3: Get Recent Messages by Phone ---")
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/conversations/phone/{TEST_PHONE.replace('+', '')}/recent",
            params={"days": 7, "limit": 50}
        )
        
        if response.status_code == 200:
            result = response.json()
            messages = result.get('data', [])
            
            print(f"✅ Retrieved {len(messages)} recent messages for {TEST_PHONE}")
            
            # Group by session
            sessions = {}
            for msg in messages:
                session_id = msg['session_id']
                if session_id not in sessions:
                    sessions[session_id] = []
                sessions[session_id].append(msg)
            
            print(f"📊 Found messages across {len(sessions)} sessions")
            for sid, msgs in sessions.items():
                print(f"  Session {sid}: {len(msgs)} messages")
                
        else:
            print(f"❌ Recent messages API failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing recent messages API: {e}")


async def test_database_direct_access():
    """Test direct database access to conversation messages"""
    print(f"\n🗄️  Testing Direct Database Access")
    print("=" * 60)
    
    try:
        # This would require database connection in a real test
        print("📝 Note: Direct database testing requires database connection")
        print("   In production, you would:")
        print("   1. Connect to PostgreSQL database")
        print("   2. Query conversation_sessions table")
        print("   3. Query conversation_messages table")
        print("   4. Verify data integrity and relationships")
        print("   5. Check indexes and performance")
        
    except Exception as e:
        print(f"❌ Database test error: {e}")


async def main():
    """Run all conversation storage tests"""
    print("🚀 Starting SMS Conversation Storage Tests")
    print("=" * 80)
    
    # Test 1: Webhook conversation storage
    session_id, message_ids = await test_webhook_conversation_storage()
    
    # Test 2: API endpoints (requires authentication in production)
    if session_id:
        await test_conversation_history_api(session_id)
    
    # Test 3: Database verification
    await test_database_direct_access()
    
    print("\n" + "=" * 80)
    print("🎉 SMS Conversation Storage Tests Complete!")
    print(f"📊 Session ID: {session_id}")
    print(f"📝 Message IDs stored: {len(message_ids)}")
    print("\n💡 Next Steps:")
    print("   1. Check database tables: conversation_sessions, conversation_messages")
    print("   2. Verify webhook records in webhooks table")
    print("   3. Test API endpoints with proper authentication")
    print("   4. Monitor conversation flow in production")


if __name__ == "__main__":
    asyncio.run(main())
