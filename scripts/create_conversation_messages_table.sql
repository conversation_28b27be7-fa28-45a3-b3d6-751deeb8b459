-- Create conversation_messages table for storing individual SMS conversation messages
-- This table provides full conversation history tracking

CREATE TABLE IF NOT EXISTS conversation_messages (
    -- Primary key
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Session association
    session_id UUID NOT NULL REFERENCES conversation_sessions(id) ON DELETE CASCADE,
    
    -- Message details
    message_content TEXT NOT NULL,
    message_type VARCHAR(20) NOT NULL CHECK (message_type IN ('user_input', 'ai_response')),
    
    -- Message metadata
    phone_number VARCHAR(20) NOT NULL,
    message_sequence INTEGER NOT NULL,
    
    -- AI processing metadata
    ai_processing_time_ms INTEGER,
    ai_model_used VARCHAR(50),
    rag_sources_used JSONB,
    conversation_stage VARCHAR(50),
    
    -- Webhook integration
    webhook_id UUID REFERENCES webhooks(id),
    webhook_message_id VARCHAR(255),
    
    -- Message status and delivery
    delivery_status VARCHAR(20) CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed')),
    delivery_timestamp TIMESTAMPTZ,
    error_message TEXT,
    
    -- Standard audit fields
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    UNIQUE(session_id, message_sequence)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversation_messages_session_id ON conversation_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_phone_number ON conversation_messages(phone_number);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_message_type ON conversation_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_conversation_stage ON conversation_messages(conversation_stage);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at ON conversation_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_webhook_message_id ON conversation_messages(webhook_message_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_delivery_status ON conversation_messages(delivery_status);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_conversation_messages_session_sequence ON conversation_messages(session_id, message_sequence);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_phone_created ON conversation_messages(phone_number, created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_type_stage ON conversation_messages(message_type, conversation_stage);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_delivery ON conversation_messages(delivery_status, delivery_timestamp);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_conversation_messages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_conversation_messages_updated_at
    BEFORE UPDATE ON conversation_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_messages_updated_at();

-- Add comments for documentation
COMMENT ON TABLE conversation_messages IS 'Individual conversation messages for SMS chat history tracking';
COMMENT ON COLUMN conversation_messages.session_id IS 'Reference to the conversation session';
COMMENT ON COLUMN conversation_messages.message_content IS 'The actual message content (user input or AI response)';
COMMENT ON COLUMN conversation_messages.message_type IS 'Type of message: user_input or ai_response';
COMMENT ON COLUMN conversation_messages.phone_number IS 'Phone number for quick filtering (denormalized for performance)';
COMMENT ON COLUMN conversation_messages.message_sequence IS 'Sequential number of message within the session';
COMMENT ON COLUMN conversation_messages.ai_processing_time_ms IS 'Time taken to generate AI response in milliseconds';
COMMENT ON COLUMN conversation_messages.ai_model_used IS 'AI model used for response generation';
COMMENT ON COLUMN conversation_messages.rag_sources_used IS 'RAG sources and metadata used for AI response generation';
COMMENT ON COLUMN conversation_messages.conversation_stage IS 'Conversation stage when message was sent';
COMMENT ON COLUMN conversation_messages.webhook_id IS 'Reference to the webhook that triggered this message';
COMMENT ON COLUMN conversation_messages.webhook_message_id IS 'External message ID from webhook provider';
COMMENT ON COLUMN conversation_messages.delivery_status IS 'Delivery status for AI responses';
COMMENT ON COLUMN conversation_messages.delivery_timestamp IS 'When the message was actually delivered';
COMMENT ON COLUMN conversation_messages.error_message IS 'Error message if delivery failed';
