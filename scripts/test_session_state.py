#!/usr/bin/env python3
"""
Test Session State Script
Check the conversation session state directly in the database
"""

import asyncio
import httpx
from datetime import datetime
from typing import Dict, Any

# Test phone number
TEST_PHONE = "61430250076"

# Webhook payload template
WEBHOOK_TEMPLATE = {
    "event_type": "SMS_INBOUND",
    "timestamp": "2025-07-02T14:39:54Z",
    "webhook_id": "1b39ae29-f723-4426-9d35-efa763959c1b",
    "webhook_name": "openxcell_webbook",
    "mo": {
        "type": "SMS",
        "id": "93d34863-2756-4d27-8e11-66441081da88",
        "sender": TEST_PHONE,
        "recipient": "61430250079",
        "message": "",
        "last_message": {
            "id": "7f507779-6a4e-48bd-a7f1-488a29d82440",
            "type": "SMS",
            "sender": TEST_PHONE,
            "recipient": "61430250079",
            "message": "hello there",
            "message_ref": "a0322322-5a6b-48cc-9c03-92425887c3c5"
        }
    }
}

async def send_webhook_message(message: str) -> Dict[str, Any]:
    """Send a webhook message to the API"""
    webhook_data = WEBHOOK_TEMPLATE.copy()
    webhook_data["mo"]["message"] = message
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/webhooks/webhooks/kudosity",
            json=webhook_data,
            timeout=30.0
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            return {"success": False, "error": response.text}

def print_step(step: str, message: str, response: Dict[str, Any]):
    """Print conversation step details"""
    print(f"\n{'='*60}")
    print(f"📋 STEP {step}")
    print(f"{'='*60}")
    print(f"📱 User Message: {message}")
    print(f"🤖 AI Response: {response.get('data', {}).get('ai_response', 'No response')}")
    
    # Extract metadata if available
    metadata = response.get('data', {}).get('ai_metadata', {})
    if metadata:
        print(f"📊 Stage: {metadata.get('conversation_stage', 'unknown')}")
        print(f"🆔 Session: {metadata.get('session_id', 'unknown')}")
    
    print(f"✅ Success: {response.get('success', False)}")
    if response.get('data', {}).get('ai_error'):
        print(f"❌ Error: {response['data']['ai_error']}")
    print(f"{'='*60}")

async def test_session_state():
    """Test the session state step by step"""
    print("🚀 Starting Session State Test")
    print("="*60)
    
    # Step 1: Send "Yes" to trigger stage change
    print("\n🔄 Step 1: Send 'Yes' to trigger stage change")
    response1 = await send_webhook_message("Yes")
    print_step("1", "Yes", response1)
    
    # Step 2: Send "Aditya" to test name collection
    print("\n🔄 Step 2: Send 'Aditya' for name collection")
    response2 = await send_webhook_message("Aditya")
    print_step("2", "Aditya", response2)
    
    # Step 3: Send another message to see if stage persists
    print("\n🔄 Step 3: Send another message to test stage persistence")
    response3 = await send_webhook_message("I'm interested in business")
    print_step("3", "I'm interested in business", response3)
    
    print("\n🎉 Session State Test Completed!")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(test_session_state()) 