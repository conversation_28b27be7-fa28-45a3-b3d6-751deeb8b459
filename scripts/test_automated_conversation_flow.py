#!/usr/bin/env python3
"""
Automated Conversation Flow Test Script
Tests the complete conversation flow from initial greeting to doc_qa stage
"""

import asyncio
import json
import httpx
from datetime import datetime
from typing import Dict, Any

# Test phone number
TEST_PHONE = "61430250076"

# Webhook payload template
WEBHOOK_TEMPLATE = {
    "event_type": "SMS_INBOUND",
    "timestamp": "2025-07-02T14:39:54Z",
    "webhook_id": "1b39ae29-f723-4426-9d35-efa763959c1b",
    "webhook_name": "openxcell_webbook",
    "mo": {
        "type": "SMS",
        "id": "93d34863-2756-4d27-8e11-66441081da88",
        "sender": TEST_PHONE,
        "recipient": "61430250079",
        "message": "",
        "last_message": {
            "id": "7f507779-6a4e-48bd-a7f1-488a29d82440",
            "type": "SMS",
            "sender": TEST_PHONE,
            "recipient": "61430250079",
            "message": "hello there",
            "message_ref": "a0322322-5a6b-48cc-9c03-92425887c3c5"
        }
    }
}

async def send_webhook_message(message: str) -> Dict[str, Any]:
    """Send a webhook message to the API"""
    webhook_data = WEBHOOK_TEMPLATE.copy()
    webhook_data["mo"]["message"] = message
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/webhooks/webhooks/kudosity",
            json=webhook_data,
            timeout=30.0
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            return {"success": False, "error": response.text}

def print_step(step: str, message: str, response: Dict[str, Any]):
    """Print conversation step details"""
    print(f"\n{'='*60}")
    print(f"📋 STEP {step}")
    print(f"{'='*60}")
    print(f"📱 User Message: {message}")
    print(f"🤖 AI Response: {response.get('data', {}).get('ai_response', 'No response')}")
    
    # Extract metadata if available
    metadata = response.get('data', {}).get('ai_metadata', {})
    if metadata:
        print(f"📊 Stage: {metadata.get('conversation_stage', 'unknown')}")
        print(f"🆔 Session: {metadata.get('session_id', 'unknown')}")
    
    print(f"✅ Success: {response.get('success', False)}")
    if response.get('data', {}).get('ai_error'):
        print(f"❌ Error: {response['data']['ai_error']}")
    print(f"{'='*60}")

async def test_conversation_flow():
    """Test the complete conversation flow"""
    print("🚀 Starting Automated Conversation Flow Test")
    print("="*60)
    
    # Step 1: Initial greeting
    print("\n🔄 Step 1: Initial Greeting")
    response1 = await send_webhook_message("Hello")
    print_step("1", "Hello", response1)
    
    # Step 2: Positive response to continue
    print("\n🔄 Step 2: Positive Response")
    response2 = await send_webhook_message("Yes")
    print_step("2", "Yes", response2)
    
    # Step 3: Provide name
    print("\n🔄 Step 3: Provide Name")
    response3 = await send_webhook_message("Aditya")
    print_step("3", "Aditya", response3)
    
    # Step 4: Answer qualification questions
    print("\n🔄 Step 4: Answer Qualification Questions")
    response4 = await send_webhook_message("I'm interested in business opportunities")
    print_step("4", "I'm interested in business opportunities", response4)
    
    # Step 5: Continue with more qualification
    print("\n🔄 Step 5: Continue Qualification")
    response5 = await send_webhook_message("Yes, I have some capital")
    print_step("5", "Yes, I have some capital", response5)
    
    # Step 6: Move to document Q&A
    print("\n🔄 Step 6: Document Q&A Introduction")
    response6 = await send_webhook_message("Tell me about the franchise")
    print_step("6", "Tell me about the franchise", response6)
    
    print("\n🎉 Conversation Flow Test Completed!")
    print("="*60)
    print("📊 Summary:")
    print("✅ Initial greeting processed")
    print("✅ Positive response handled")
    print("✅ Name collection completed")
    print("✅ Qualification questions answered")
    print("✅ Document Q&A stage reached")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(test_conversation_flow()) 