"""
Comprehensive tests for conversation logs functionality
Tests for model, repository, service, and endpoints
"""

import pytest
import pytest_asyncio
from datetime import datetime, timezone
from uuid import uuid4, UUID
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.conversation_log import ConversationLog
from app.models.lead import Lead
from app.repositories.conversation_log_repository import ConversationLogRepository
from app.services.conversation_log_service import ConversationLogService
from app.schemas.conversation_log import (
    ConversationLogCreate, 
    ConversationLogUpdate, 
    ConversationLogFilter,
    SenderType
)
from tests.conftest import TestingSessionLocal


class TestConversationLogModel:
    """Test ConversationLog model"""
    
    @pytest.mark.asyncio
    async def test_conversation_log_creation(self, db_session: AsyncSession):
        """Test creating a conversation log entry"""
        # Create a test lead first
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create conversation log
        conversation_log = ConversationLog(
            lead_id=lead.id,
            sender="user",
            message="Hello, I'm interested in franchises",
            timestamp=datetime.now(timezone.utc)
        )
        
        db_session.add(conversation_log)
        await db_session.commit()
        await db_session.refresh(conversation_log)
        
        assert conversation_log.id is not None
        assert conversation_log.lead_id == lead.id
        assert conversation_log.sender == "user"
        assert conversation_log.message == "Hello, I'm interested in franchises"
        assert conversation_log.is_active is True
        assert conversation_log.is_deleted is False
        assert conversation_log.created_at is not None
        assert conversation_log.updated_at is not None

    @pytest.mark.asyncio
    async def test_conversation_log_relationships(self, db_session: AsyncSession):
        """Test conversation log relationships"""
        # Create a test lead
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create conversation log
        conversation_log = ConversationLog(
            lead_id=lead.id,
            sender="system",
            message="Thank you for your interest!",
            timestamp=datetime.now(timezone.utc)
        )
        
        db_session.add(conversation_log)
        await db_session.commit()
        await db_session.refresh(conversation_log)
        
        # Test relationship
        assert conversation_log.lead is not None
        assert conversation_log.lead.id == lead.id
        assert conversation_log.lead.first_name == "Test"

    def test_conversation_log_repr(self):
        """Test conversation log string representation"""
        log_id = uuid4()
        lead_id = uuid4()
        timestamp = datetime.now(timezone.utc)
        
        conversation_log = ConversationLog(
            id=log_id,
            lead_id=lead_id,
            sender="user",
            message="Test message",
            timestamp=timestamp
        )
        
        expected_repr = f"<ConversationLog(id={log_id}, lead_id={lead_id}, sender=user, timestamp={timestamp})>"
        assert repr(conversation_log) == expected_repr


class TestConversationLogRepository:
    """Test ConversationLogRepository"""
    
    @pytest.mark.asyncio
    async def test_create_conversation_log(self, db_session: AsyncSession):
        """Test creating conversation log via repository"""
        # Create test lead
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create repository
        repo = ConversationLogRepository(db_session)
        
        # Create conversation log data
        log_data = ConversationLogCreate(
            lead_id=lead.id,
            sender=SenderType.USER,
            message="Test message from repository"
        )
        
        # Create via repository
        conversation_log = await repo.create(log_data)
        
        assert conversation_log.id is not None
        assert conversation_log.lead_id == lead.id
        assert conversation_log.sender == "user"
        assert conversation_log.message == "Test message from repository"
        assert conversation_log.is_active is True
        assert conversation_log.is_deleted is False

    @pytest.mark.asyncio
    async def test_get_by_id(self, db_session: AsyncSession):
        """Test getting conversation log by ID"""
        # Create test data
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        conversation_log = ConversationLog(
            lead_id=lead.id,
            sender="user",
            message="Test message",
            timestamp=datetime.now(timezone.utc)
        )
        db_session.add(conversation_log)
        await db_session.commit()
        await db_session.refresh(conversation_log)
        
        # Test repository
        repo = ConversationLogRepository(db_session)
        retrieved_log = await repo.get_by_id(conversation_log.id)
        
        assert retrieved_log is not None
        assert retrieved_log.id == conversation_log.id
        assert retrieved_log.message == "Test message"

    @pytest.mark.asyncio
    async def test_get_by_lead_id(self, db_session: AsyncSession):
        """Test getting conversation logs by lead ID"""
        # Create test lead
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create multiple conversation logs
        logs = []
        for i in range(3):
            log = ConversationLog(
                lead_id=lead.id,
                sender="user" if i % 2 == 0 else "system",
                message=f"Test message {i}",
                timestamp=datetime.now(timezone.utc)
            )
            logs.append(log)
            db_session.add(log)
        
        await db_session.commit()
        
        # Test repository
        repo = ConversationLogRepository(db_session)
        retrieved_logs = await repo.get_by_lead_id(lead.id)
        
        assert len(retrieved_logs) == 3
        assert all(log.lead_id == lead.id for log in retrieved_logs)

    @pytest.mark.asyncio
    async def test_update_conversation_log(self, db_session: AsyncSession):
        """Test updating conversation log"""
        # Create test data
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        conversation_log = ConversationLog(
            lead_id=lead.id,
            sender="user",
            message="Original message",
            timestamp=datetime.now(timezone.utc)
        )
        db_session.add(conversation_log)
        await db_session.commit()
        await db_session.refresh(conversation_log)
        
        # Test repository update
        repo = ConversationLogRepository(db_session)
        update_data = ConversationLogUpdate(message="Updated message")
        
        updated_log = await repo.update(conversation_log.id, update_data)
        
        assert updated_log is not None
        assert updated_log.message == "Updated message"
        assert updated_log.id == conversation_log.id

    @pytest.mark.asyncio
    async def test_soft_delete(self, db_session: AsyncSession):
        """Test soft deleting conversation log"""
        # Create test data
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        conversation_log = ConversationLog(
            lead_id=lead.id,
            sender="user",
            message="Test message",
            timestamp=datetime.now(timezone.utc)
        )
        db_session.add(conversation_log)
        await db_session.commit()
        await db_session.refresh(conversation_log)
        
        # Test repository soft delete
        repo = ConversationLogRepository(db_session)
        result = await repo.soft_delete(conversation_log.id)
        
        assert result is True
        
        # Verify soft delete
        deleted_log = await repo.get_by_id(conversation_log.id)
        assert deleted_log is None  # Should not be returned by get_by_id (excludes deleted)


class TestConversationLogService:
    """Test ConversationLogService"""
    
    @pytest.mark.asyncio
    async def test_store_conversation_message(self, db_session: AsyncSession):
        """Test storing conversation message via service"""
        # Create test lead
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create service
        repo = ConversationLogRepository(db_session)
        service = ConversationLogService(repo)
        
        # Store user message
        user_log = await service.store_conversation_message(
            lead_id=lead.id,
            sender="user",
            message="Hello, I need help with franchises"
        )
        
        assert user_log.lead_id == lead.id
        assert user_log.sender == "user"
        assert user_log.message == "Hello, I need help with franchises"
        
        # Store system message
        system_log = await service.store_conversation_message(
            lead_id=lead.id,
            sender="system",
            message="I'm here to help you with franchise information"
        )
        
        assert system_log.lead_id == lead.id
        assert system_log.sender == "system"
        assert system_log.message == "I'm here to help you with franchise information"

    @pytest.mark.asyncio
    async def test_store_user_message(self, db_session: AsyncSession):
        """Test storing user message via service"""
        # Create test lead
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create service
        repo = ConversationLogRepository(db_session)
        service = ConversationLogService(repo)
        
        # Store user message
        log = await service.store_user_message(
            lead_id=lead.id,
            message="User message test"
        )
        
        assert log.sender == "user"
        assert log.message == "User message test"

    @pytest.mark.asyncio
    async def test_store_system_message(self, db_session: AsyncSession):
        """Test storing system message via service"""
        # Create test lead
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create service
        repo = ConversationLogRepository(db_session)
        service = ConversationLogService(repo)
        
        # Store system message
        log = await service.store_system_message(
            lead_id=lead.id,
            message="System response test"
        )
        
        assert log.sender == "system"
        assert log.message == "System response test"

    @pytest.mark.asyncio
    async def test_get_conversation_history(self, db_session: AsyncSession):
        """Test getting conversation history via service"""
        # Create test lead
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create service and store messages
        repo = ConversationLogRepository(db_session)
        service = ConversationLogService(repo)
        
        await service.store_user_message(lead.id, "Hello")
        await service.store_system_message(lead.id, "Hi there!")
        await service.store_user_message(lead.id, "I need help")
        
        # Get conversation history
        history = await service.get_conversation_history(lead.id)
        
        assert len(history) == 3
        # Should be ordered by timestamp desc (most recent first)
        assert history[0].message == "I need help"
        assert history[1].message == "Hi there!"
        assert history[2].message == "Hello"

    @pytest.mark.asyncio
    async def test_message_sanitization(self, db_session: AsyncSession):
        """Test message content sanitization"""
        # Create test lead
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create service
        repo = ConversationLogRepository(db_session)
        service = ConversationLogService(repo)
        
        # Test message with potentially harmful content
        malicious_message = "<script>alert('xss')</script>Hello & goodbye"
        
        log = await service.store_user_message(
            lead_id=lead.id,
            message=malicious_message
        )
        
        # Should be HTML escaped
        assert "<script>" not in log.message
        assert "&lt;script&gt;" in log.message
        assert "&amp;" in log.message

    @pytest.mark.asyncio
    async def test_invalid_sender_validation(self, db_session: AsyncSession):
        """Test validation of sender field"""
        # Create test lead
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)
        
        # Create service
        repo = ConversationLogRepository(db_session)
        service = ConversationLogService(repo)
        
        # Test invalid sender
        with pytest.raises(ValueError, match="Invalid sender"):
            await service.store_conversation_message(
                lead_id=lead.id,
                sender="invalid_sender",
                message="Test message"
            )


class TestConversationLogEndpoints:
    """Test ConversationLog API endpoints"""

    @pytest.mark.asyncio
    async def test_list_conversation_logs_unauthorized(self, client: AsyncClient):
        """Test listing conversation logs without authentication"""
        response = await client.get("/api/v1/conversation-logs/")
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_list_conversation_logs_authorized(self, client: AsyncClient, auth_headers: dict, db_session: AsyncSession):
        """Test listing conversation logs with authentication"""
        # Create test data
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)

        # Create conversation logs
        for i in range(3):
            log = ConversationLog(
                lead_id=lead.id,
                sender="user" if i % 2 == 0 else "system",
                message=f"Test message {i}",
                timestamp=datetime.now(timezone.utc)
            )
            db_session.add(log)

        await db_session.commit()

        # Test endpoint
        response = await client.get(
            "/api/v1/conversation-logs/",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "items" in data["data"]
        assert len(data["data"]["items"]) >= 3

    @pytest.mark.asyncio
    async def test_get_conversation_log_by_id(self, client: AsyncClient, auth_headers: dict, db_session: AsyncSession):
        """Test getting conversation log by ID"""
        # Create test data
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)

        conversation_log = ConversationLog(
            lead_id=lead.id,
            sender="user",
            message="Test message for ID retrieval",
            timestamp=datetime.now(timezone.utc)
        )
        db_session.add(conversation_log)
        await db_session.commit()
        await db_session.refresh(conversation_log)

        # Test endpoint
        response = await client.get(
            f"/api/v1/conversation-logs/{conversation_log.id}",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == str(conversation_log.id)
        assert data["data"]["message"] == "Test message for ID retrieval"

    @pytest.mark.asyncio
    async def test_get_conversation_log_not_found(self, client: AsyncClient, auth_headers: dict):
        """Test getting non-existent conversation log"""
        fake_id = uuid4()
        response = await client.get(
            f"/api/v1/conversation-logs/{fake_id}",
            headers=auth_headers
        )

        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False

    @pytest.mark.asyncio
    async def test_get_lead_conversation_history(self, client: AsyncClient, auth_headers: dict, db_session: AsyncSession):
        """Test getting conversation history for a lead"""
        # Create test data
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)

        # Create conversation logs
        messages = ["Hello", "Hi there!", "I need help", "How can I assist you?"]
        for i, message in enumerate(messages):
            log = ConversationLog(
                lead_id=lead.id,
                sender="user" if i % 2 == 0 else "system",
                message=message,
                timestamp=datetime.now(timezone.utc)
            )
            db_session.add(log)

        await db_session.commit()

        # Test endpoint
        response = await client.get(
            f"/api/v1/conversation-logs/lead/{lead.id}",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "messages" in data["data"]
        assert "stats" in data["data"]
        assert len(data["data"]["messages"]) == 4

    @pytest.mark.asyncio
    async def test_update_conversation_log(self, client: AsyncClient, auth_headers: dict, db_session: AsyncSession):
        """Test updating conversation log"""
        # Create test data
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)

        conversation_log = ConversationLog(
            lead_id=lead.id,
            sender="user",
            message="Original message",
            timestamp=datetime.now(timezone.utc)
        )
        db_session.add(conversation_log)
        await db_session.commit()
        await db_session.refresh(conversation_log)

        # Test endpoint
        update_data = {
            "message": "Updated message content"
        }

        response = await client.put(
            f"/api/v1/conversation-logs/{conversation_log.id}",
            json=update_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["message"] == "Updated message content"

    @pytest.mark.asyncio
    async def test_delete_conversation_log(self, client: AsyncClient, auth_headers: dict, db_session: AsyncSession):
        """Test deleting conversation log"""
        # Create test data
        lead = Lead(
            first_name="Test",
            last_name="User",
            phone="+1234567890",
            email="<EMAIL>"
        )
        db_session.add(lead)
        await db_session.commit()
        await db_session.refresh(lead)

        conversation_log = ConversationLog(
            lead_id=lead.id,
            sender="user",
            message="Message to be deleted",
            timestamp=datetime.now(timezone.utc)
        )
        db_session.add(conversation_log)
        await db_session.commit()
        await db_session.refresh(conversation_log)

        # Test endpoint
        response = await client.delete(
            f"/api/v1/conversation-logs/{conversation_log.id}",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["deleted_id"] == str(conversation_log.id)

        # Verify deletion
        get_response = await client.get(
            f"/api/v1/conversation-logs/{conversation_log.id}",
            headers=auth_headers
        )
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_with_filters(self, client: AsyncClient, auth_headers: dict, db_session: AsyncSession):
        """Test listing conversation logs with filters"""
        # Create test data
        lead1 = Lead(first_name="Test1", last_name="User1", phone="+1111111111", email="<EMAIL>")
        lead2 = Lead(first_name="Test2", last_name="User2", phone="+2222222222", email="<EMAIL>")

        db_session.add_all([lead1, lead2])
        await db_session.commit()
        await db_session.refresh(lead1)
        await db_session.refresh(lead2)

        # Create logs for both leads
        logs = [
            ConversationLog(lead_id=lead1.id, sender="user", message="User message 1", timestamp=datetime.now(timezone.utc)),
            ConversationLog(lead_id=lead1.id, sender="system", message="System response 1", timestamp=datetime.now(timezone.utc)),
            ConversationLog(lead_id=lead2.id, sender="user", message="User message 2", timestamp=datetime.now(timezone.utc)),
        ]

        db_session.add_all(logs)
        await db_session.commit()

        # Test filtering by lead_id
        response = await client.get(
            f"/api/v1/conversation-logs/?lead_id={lead1.id}",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]["items"]) == 2
        assert all(item["lead_id"] == str(lead1.id) for item in data["data"]["items"])

        # Test filtering by sender
        response = await client.get(
            "/api/v1/conversation-logs/?sender=user",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # Should have at least 2 user messages
        user_messages = [item for item in data["data"]["items"] if item["sender"] == "user"]
        assert len(user_messages) >= 2
