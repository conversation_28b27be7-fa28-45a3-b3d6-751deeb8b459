"""
Master Test Suite for GrowthHive API
Comprehensive test suite covering all endpoints with and without authentication
"""

import pytest_asyncio
from httpx import AsyncClient, ASGITransport
from app.main import app
import uuid
import random
from datetime import datetime
from typing import Dict, Any


class MasterTestSuite:
    """Comprehensive test suite for all GrowthHive API endpoints"""
    
    def __init__(self):
        self.base_url = "http://test"
        # Generate unique mobile number (valid format: +1 followed by 10 digits)
        unique_mobile = f"+1{random.randint(1000000000, 9999999999)}"
        self.test_user_data = {
            "email": f"test_{uuid.uuid4().hex[:8]}@example.com",
            "password": "TestPassword123!",
            "confirm_password": "TestPassword123!",
            "mobile": unique_mobile,
            "first_name": "Test",
            "last_name": "User",
            "role": "ADMIN"
        }
        self.auth_headers = {}
        self.test_data = {}
        
    def setup_client(self) -> AsyncClient:
        """Setup async client for testing"""
        transport = ASGITransport(app=app)
        return AsyncClient(transport=transport, base_url=self.base_url)

    def safe_json_response(self, response):
        """Safely parse JSON response or return text"""
        try:
            if response.status_code < 500 and response.content:
                return response.json()
            else:
                return response.text
        except Exception:
            return response.text
    
    async def register_and_login(self, client: AsyncClient) -> Dict[str, str]:
        """Register a test user and login to get auth headers"""
        print(f"\n🔐 Registering test user: {self.test_user_data['email']}")
        
        # Register user
        register_response = await client.post("/api/auth/register", json=self.test_user_data)
        print(f"Registration Status: {register_response.status_code}")
        if register_response.status_code not in [200, 201]:
            print(f"Registration failed: {register_response.text}")
            
        # Login user
        login_data = {
            "email_or_mobile": self.test_user_data["email"],
            "password": self.test_user_data["password"],
            "remember_me": False
        }
        
        login_response = await client.post("/api/auth/login", json=login_data)
        print(f"Login Status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_json = self.safe_json_response(login_response)
            if isinstance(login_json, dict) and login_json.get("data") and login_json["data"].get("details"):
                token = login_json["data"]["details"]["access_token"]
                self.auth_headers = {"Authorization": f"Bearer {token}"}
                print("✅ Authentication successful")
                return self.auth_headers
        
        print(f"Login failed: {login_response.text}")
        return {}
    
    async def test_authentication_endpoints(self, client: AsyncClient):
        """Test all authentication endpoints"""
        print("\n" + "="*50)
        print("🔐 TESTING AUTHENTICATION ENDPOINTS")
        print("="*50)
        
        results = {}
        
        # Test Registration (no auth required)
        print("\n📝 Testing Registration...")
        unique_email = f"reg_test_{uuid.uuid4().hex[:8]}@example.com"
        # Generate unique mobile for registration test
        unique_mobile_reg = f"+1{random.randint(1000000000, 9999999999)}"
        register_data = {
            **self.test_user_data,
            "email": unique_email,
            "mobile": unique_mobile_reg
        }
        
        response = await client.post("/api/auth/register", json=register_data)
        results["register"] = {
            "status_code": response.status_code,
            "success": response.status_code in [200, 201],
            "response": self.safe_json_response(response)
        }
        print(f"Registration: {'✅ PASS' if results['register']['success'] else '❌ FAIL'} ({response.status_code})")
        
        # Test Login (no auth required)
        print("\n🔑 Testing Login...")
        login_data = {
            "email_or_mobile": unique_email,
            "password": register_data["password"],
            "remember_me": False
        }
        
        response = await client.post("/api/auth/login", json=login_data)
        results["login"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": self.safe_json_response(response)
        }
        print(f"Login: {'✅ PASS' if results['login']['success'] else '❌ FAIL'} ({response.status_code})")
        
        # Get token for authenticated tests
        if results["login"]["success"]:
            login_json = self.safe_json_response(response)
            if isinstance(login_json, dict) and login_json.get("data") and login_json["data"].get("details"):
                token = login_json["data"]["details"]["access_token"]
                temp_headers = {"Authorization": f"Bearer {token}"}
                
                # Test Get Current User (auth required)
                print("\n👤 Testing Get Current User...")
                response = await client.get("/api/auth/me", headers=temp_headers)
                results["get_me"] = {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response": self.safe_json_response(response)
                }
                print(f"Get Me: {'✅ PASS' if results['get_me']['success'] else '❌ FAIL'} ({response.status_code})")
                
                # Test Logout (auth required)
                print("\n🚪 Testing Logout...")
                response = await client.post("/api/auth/logout", headers=temp_headers)
                results["logout"] = {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response": self.safe_json_response(response)
                }
                print(f"Logout: {'✅ PASS' if results['logout']['success'] else '❌ FAIL'} ({response.status_code})")
        
        return results
    
    async def test_categories_endpoints(self, client: AsyncClient):
        """Test all categories endpoints"""
        print("\n" + "="*50)
        print("📂 TESTING CATEGORIES ENDPOINTS")
        print("="*50)
        
        results = {}
        
        # Test without auth first
        print("\n🚫 Testing Categories without Authentication...")
        
        # List categories without auth
        response = await client.get("/api/categories")
        results["list_categories_no_auth"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,  # Should be unauthorized
            "response": self.safe_json_response(response)
        }
        print(f"List Categories (No Auth): {'✅ PASS' if results['list_categories_no_auth']['success'] else '❌ FAIL'} ({response.status_code})")
        
        # Test with auth
        if self.auth_headers:
            print("\n🔐 Testing Categories with Authentication...")
            
            # List categories with auth
            response = await client.get("/api/categories", headers=self.auth_headers)
            results["list_categories_auth"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"List Categories (Auth): {'✅ PASS' if results['list_categories_auth']['success'] else '❌ FAIL'} ({response.status_code})")

            # Create category
            category_data = {
                "name": f"Test Category {uuid.uuid4().hex[:8]}",
                "description": "Test category description"
            }
            response = await client.post("/api/categories", json=category_data, headers=self.auth_headers)
            results["create_category"] = {
                "status_code": response.status_code,
                "success": response.status_code in [200, 201],
                "response": self.safe_json_response(response)
            }
            print(f"Create Category: {'✅ PASS' if results['create_category']['success'] else '❌ FAIL'} ({response.status_code})")

            # Store category ID for further tests
            if results["create_category"]["success"]:
                category_response = self.safe_json_response(response)
                if isinstance(category_response, dict) and category_response.get("data") and category_response["data"].get("id"):
                    self.test_data["category_id"] = category_response["data"]["id"]

                    # Get category by ID
                    response = await client.get(f"/api/categories/{self.test_data['category_id']}", headers=self.auth_headers)
                    results["get_category"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Get Category: {'✅ PASS' if results['get_category']['success'] else '❌ FAIL'} ({response.status_code})")
        
        return results

    async def test_subcategories_endpoints(self, client: AsyncClient):
        """Test all subcategories endpoints"""
        print("\n" + "="*50)
        print("📁 TESTING SUBCATEGORIES ENDPOINTS")
        print("="*50)

        results = {}

        # Test without auth first
        print("\n🚫 Testing Subcategories without Authentication...")

        # List subcategories without auth
        response = await client.get("/api/subcategories")
        results["list_subcategories_no_auth"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,  # Should be unauthorized
            "response": self.safe_json_response(response)
        }
        print(f"List Subcategories (No Auth): {'✅ PASS' if results['list_subcategories_no_auth']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test with auth
        if self.auth_headers:
            print("\n🔐 Testing Subcategories with Authentication...")

            # List subcategories with auth
            response = await client.get("/api/subcategories", headers=self.auth_headers)
            results["list_subcategories_auth"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"List Subcategories (Auth): {'✅ PASS' if results['list_subcategories_auth']['success'] else '❌ FAIL'} ({response.status_code})")

            # Only create subcategory if we have a category_id from previous tests
            if self.test_data.get("category_id"):
                # Create subcategory
                subcategory_data = {
                    "name": f"Test Subcategory {uuid.uuid4().hex[:8]}",
                    "description": "Test subcategory description",
                    "category_id": self.test_data["category_id"]
                }
                response = await client.post("/api/subcategories", json=subcategory_data, headers=self.auth_headers)
                results["create_subcategory"] = {
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 201],
                    "response": self.safe_json_response(response)
                }
                print(f"Create Subcategory: {'✅ PASS' if results['create_subcategory']['success'] else '❌ FAIL'} ({response.status_code})")

                # Store subcategory ID for further tests
                if results["create_subcategory"]["success"]:
                    subcategory_response = self.safe_json_response(response)
                    if isinstance(subcategory_response, dict) and subcategory_response.get("data") and subcategory_response["data"].get("id"):
                        self.test_data["subcategory_id"] = subcategory_response["data"]["id"]

                        # Get subcategory by ID
                        response = await client.get(f"/api/subcategories/{self.test_data['subcategory_id']}", headers=self.auth_headers)
                        results["get_subcategory"] = {
                            "status_code": response.status_code,
                            "success": response.status_code == 200,
                            "response": self.safe_json_response(response)
                        }
                        print(f"Get Subcategory: {'✅ PASS' if results['get_subcategory']['success'] else '❌ FAIL'} ({response.status_code})")
            else:
                print("⚠️ Skipping subcategory creation - no category_id available")

        return results

    async def test_franchisors_endpoints(self, client: AsyncClient):
        """Test all franchisor endpoints"""
        print("\n" + "="*50)
        print("🏢 TESTING FRANCHISORS ENDPOINTS")
        print("="*50)

        results = {}

        # Test without auth first
        print("\n🚫 Testing Franchisors without Authentication...")

        # List franchisors without auth
        response = await client.get("/api/franchisors/")  # Add trailing slash
        results["list_franchisors_no_auth"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,  # Should be unauthorized
            "response": self.safe_json_response(response)
        }
        print(f"List Franchisors (No Auth): {'✅ PASS' if results['list_franchisors_no_auth']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test with auth
        if self.auth_headers:
            print("\n🔐 Testing Franchisors with Authentication...")

            # List franchisors with auth
            response = await client.get("/api/franchisors/", headers=self.auth_headers)  # Add trailing slash
            results["list_franchisors_auth"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"List Franchisors (Auth): {'✅ PASS' if results['list_franchisors_auth']['success'] else '❌ FAIL'} ({response.status_code})")

            # Create franchisor
            franchisor_data = {
                "name": f"Test Franchisor {uuid.uuid4().hex[:8]}",
                "category": "food_beverage",
                "region": "australia",
                "budget": 150000.0,
                "sub_category": "restaurant"
            }
            response = await client.post("/api/franchisors/", json=franchisor_data, headers=self.auth_headers)  # Add trailing slash
            results["create_franchisor"] = {
                "status_code": response.status_code,
                "success": response.status_code in [200, 201],
                "response": self.safe_json_response(response)
            }
            print(f"Create Franchisor: {'✅ PASS' if results['create_franchisor']['success'] else '❌ FAIL'} ({response.status_code})")

            # Store franchisor ID for further tests
            if results["create_franchisor"]["success"]:
                franchisor_response = self.safe_json_response(response)
                if isinstance(franchisor_response, dict) and franchisor_response.get("data") and franchisor_response["data"].get("id"):
                    self.test_data["franchisor_id"] = franchisor_response["data"]["id"]

                    # Get franchisor by ID
                    response = await client.get(f"/api/franchisors/{self.test_data['franchisor_id']}", headers=self.auth_headers)
                    results["get_franchisor"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Get Franchisor: {'✅ PASS' if results['get_franchisor']['success'] else '❌ FAIL'} ({response.status_code})")

        return results

    async def test_leads_endpoints(self, client: AsyncClient):
        """Test all leads endpoints"""
        print("\n" + "="*50)
        print("👥 TESTING LEADS ENDPOINTS")
        print("="*50)

        results = {}

        # Test without auth first
        print("\n🚫 Testing Leads without Authentication...")

        # List leads without auth
        response = await client.get("/api/leads/")  # Add trailing slash
        results["list_leads_no_auth"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,  # Should be unauthorized
            "response": self.safe_json_response(response)
        }
        print(f"List Leads (No Auth): {'✅ PASS' if results['list_leads_no_auth']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test with auth
        if self.auth_headers:
            print("\n🔐 Testing Leads with Authentication...")

            # List leads with auth
            response = await client.get("/api/leads/", headers=self.auth_headers)  # Add trailing slash
            results["list_leads_auth"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"List Leads (Auth): {'✅ PASS' if results['list_leads_auth']['success'] else '❌ FAIL'} ({response.status_code})")

            # Create lead
            unique_lead_mobile = f"+1{random.randint(1000000000, 9999999999)}"
            lead_data = {
                "full_name": f"Test Lead {uuid.uuid4().hex[:8]}",
                "contact_number": unique_lead_mobile,
                "email": f"lead_{uuid.uuid4().hex[:8]}@example.com",
                "location": "Test City",
                "lead_source": "website",
                "franchise_preference": "food_beverage",
                "budget_preference": 150000.00,  # Use decimal number instead of string
                "qualification_status": "new"
            }
            response = await client.post("/api/leads/", json=lead_data, headers=self.auth_headers)  # Add trailing slash
            results["create_lead"] = {
                "status_code": response.status_code,
                "success": response.status_code in [200, 201],
                "response": self.safe_json_response(response)
            }
            print(f"Create Lead: {'✅ PASS' if results['create_lead']['success'] else '❌ FAIL'} ({response.status_code})")
            if not results['create_lead']['success']:
                print(f"Create Lead Error: {results['create_lead']['response']}")

            # Store lead ID for further tests
            if results["create_lead"]["success"]:
                lead_response = self.safe_json_response(response)
                if isinstance(lead_response, dict) and lead_response.get("data") and lead_response["data"].get("id"):
                    self.test_data["lead_id"] = lead_response["data"]["id"]

                    # Get lead by ID
                    response = await client.get(f"/api/leads/{self.test_data['lead_id']}", headers=self.auth_headers)
                    results["get_lead"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Get Lead: {'✅ PASS' if results['get_lead']['success'] else '❌ FAIL'} ({response.status_code})")

        return results

    async def test_documents_endpoints(self, client: AsyncClient):
        """Test all document endpoints"""
        print("\n" + "="*50)
        print("📄 TESTING DOCUMENTS ENDPOINTS")
        print("="*50)

        results = {}

        # Test without auth first
        print("\n🚫 Testing Documents without Authentication...")

        # List documents without auth
        response = await client.get("/api/documents/")
        results["list_documents_no_auth"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,  # Should be unauthorized
            "response": self.safe_json_response(response)
        }
        print(f"List Documents (No Auth): {'✅ PASS' if results['list_documents_no_auth']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test with auth
        if self.auth_headers:
            print("\n🔐 Testing Documents with Authentication...")

            # List documents with auth
            response = await client.get("/api/documents/", headers=self.auth_headers)
            results["list_documents_auth"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"List Documents (Auth): {'✅ PASS' if results['list_documents_auth']['success'] else '❌ FAIL'} ({response.status_code})")

            # Create document
            document_data = {
                "name": f"Test Document {uuid.uuid4().hex[:8]}",
                "description": "Test document for API testing",
                "file_path": "test/path/document.pdf",
                "file_type": "application/pdf",
                "file_size": "1024",
                "franchisor_id": self.test_data.get("franchisor_id"),
                "is_active": True
            }

            response = await client.post("/api/documents/", json=document_data, headers=self.auth_headers)
            results["create_document"] = {
                "status_code": response.status_code,
                "success": response.status_code in [200, 201],
                "response": self.safe_json_response(response)
            }
            print(f"Create Document: {'✅ PASS' if results['create_document']['success'] else '❌ FAIL'} ({response.status_code})")

            # Store document ID for further tests
            if results["create_document"]["success"]:
                document_response = self.safe_json_response(response)
                if isinstance(document_response, dict) and document_response.get("data") and document_response["data"].get("id"):
                    self.test_data["document_id"] = document_response["data"]["id"]

                    # Get document by ID
                    response = await client.get(f"/api/documents/{self.test_data['document_id']}", headers=self.auth_headers)
                    results["get_document"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Get Document: {'✅ PASS' if results['get_document']['success'] else '❌ FAIL'} ({response.status_code})")



                    # Update document status
                    status_data = {"is_active": False}
                    response = await client.patch(f"/api/documents/{self.test_data['document_id']}/status", json=status_data, headers=self.auth_headers)
                    results["update_document_status"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Update Document Status: {'✅ PASS' if results['update_document_status']['success'] else '❌ FAIL'} ({response.status_code})")

                    # Test search with filters
                    response = await client.get("/api/documents/?search=Test&is_active=false", headers=self.auth_headers)
                    results["search_documents"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Search Documents: {'✅ PASS' if results['search_documents']['success'] else '❌ FAIL'} ({response.status_code})")



            # Test pagination
            response = await client.get("/api/documents/?skip=0&limit=5", headers=self.auth_headers)
            results["paginate_documents"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"Paginate Documents: {'✅ PASS' if results['paginate_documents']['success'] else '❌ FAIL'} ({response.status_code})")

        return results

    async def test_logs_endpoints(self, client: AsyncClient):
        """Test all logs endpoints (no authentication required)"""
        print("\n" + "="*50)
        print("📋 TESTING LOGS ENDPOINTS")
        print("="*50)

        results = {}

        # Test list log files (no auth required)
        print("\n📋 Testing: List log files...")
        response = await client.get("/api/logs/")
        results["list_logs"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": self.safe_json_response(response)
        }
        print(f"List Logs: {'✅ PASS' if results['list_logs']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test health check (no auth required)
        print("\n🏥 Testing: Health check...")
        response = await client.get("/api/logs/health")
        results["health_check"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": self.safe_json_response(response)
        }
        print(f"Health Check: {'✅ PASS' if results['health_check']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test app log info (no auth required)
        print("\n📊 Testing: App log info...")
        response = await client.get("/api/logs/download/app/info")
        results["app_log_info"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": self.safe_json_response(response)
        }
        print(f"App Log Info: {'✅ PASS' if results['app_log_info']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test error log info (no auth required)
        print("\n📊 Testing: Error log info...")
        response = await client.get("/api/logs/download/error/info")
        results["error_log_info"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": self.safe_json_response(response)
        }
        print(f"Error Log Info: {'✅ PASS' if results['error_log_info']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test app log download (no auth required)
        print("\n⬇️ Testing: Download app log...")
        response = await client.get("/api/logs/download/app")
        results["download_app_log"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": "File download response" if response.status_code == 200 else self.safe_json_response(response)
        }
        print(f"Download App Log: {'✅ PASS' if results['download_app_log']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test error log download (no auth required)
        print("\n⬇️ Testing: Download error log...")
        response = await client.get("/api/logs/download/error")
        results["download_error_log"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": "File download response" if response.status_code == 200 else self.safe_json_response(response)
        }
        print(f"Download Error Log: {'✅ PASS' if results['download_error_log']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test invalid log type (should return 400)
        print("\n❌ Testing: Invalid log type...")
        response = await client.get("/api/logs/download/invalid")
        results["invalid_log_type"] = {
            "status_code": response.status_code,
            "success": response.status_code == 400,  # Should return 400 for invalid log type
            "response": self.safe_json_response(response)
        }
        print(f"Invalid Log Type: {'✅ PASS' if results['invalid_log_type']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test ZIP download (no auth required)
        print("\n📦 Testing: Download logs as ZIP...")
        response = await client.get("/api/logs/download/zip")
        results["download_logs_zip"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": "ZIP file download response" if response.status_code == 200 else self.safe_json_response(response)
        }
        print(f"Download Logs ZIP: {'✅ PASS' if results['download_logs_zip']['success'] else '❌ FAIL'} ({response.status_code})")

        return results

    async def test_holidays_endpoints(self, client: AsyncClient):
        """Test holiday management endpoints"""
        print("\n🎄 Testing Holiday Endpoints...")
        results = {}

        # Test create holiday
        print("\n➕ Testing: Create holiday...")
        # Use a unique date to avoid conflicts
        from datetime import datetime, timedelta
        import random
        unique_date = (datetime.now() + timedelta(days=365 + random.randint(1, 1000))).strftime('%Y-%m-%d')
        holiday_data = {
            "holiday_type": "PREDEFINED",
            "date": unique_date,
            "all_day": True,
            "start_time": None,
            "end_time": None,
            "description": "Test Holiday"
        }
        response = await client.post("/api/settings/holidays", json=holiday_data, headers=self.auth_headers)
        results["create_holiday"] = {
            "status_code": response.status_code,
            "success": response.status_code == 201,
            "response": self.safe_json_response(response)
        }
        print(f"Create Holiday: {'✅ PASS' if results['create_holiday']['success'] else '❌ FAIL'} ({response.status_code})")

        # Store holiday ID for other tests
        holiday_id = None
        if results["create_holiday"]["success"]:
            response_data = results["create_holiday"]["response"]["data"]["details"]
            # Find the ID field in the response details
            for item in response_data:
                if isinstance(item, list) and len(item) == 2 and item[0] == "id":
                    holiday_id = item[1]
                    break

        # Test create partial day holiday
        print("\n➕ Testing: Create partial day holiday...")
        # Use a different unique date to avoid conflicts
        partial_date = (datetime.now() + timedelta(days=400 + random.randint(1, 1000))).strftime('%Y-%m-%d')
        partial_holiday_data = {
            "holiday_type": "PERSONAL",
            "date": partial_date,
            "all_day": False,
            "start_time": "09:00:00",
            "end_time": "17:00:00",
            "description": "Test Partial Holiday"
        }
        response = await client.post("/api/settings/holidays", json=partial_holiday_data, headers=self.auth_headers)
        results["create_partial_holiday"] = {
            "status_code": response.status_code,
            "success": response.status_code == 201,
            "response": self.safe_json_response(response)
        }
        print(f"Create Partial Holiday: {'✅ PASS' if results['create_partial_holiday']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test list holidays
        print("\n📋 Testing: List holidays...")
        response = await client.get("/api/settings/holidays", headers=self.auth_headers)
        results["list_holidays"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": self.safe_json_response(response)
        }
        print(f"List Holidays: {'✅ PASS' if results['list_holidays']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test get holiday by ID
        if holiday_id:
            print(f"\n🔍 Testing: Get holiday by ID ({holiday_id})...")
            response = await client.get(f"/api/settings/holidays/{holiday_id}", headers=self.auth_headers)
            results["get_holiday"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"Get Holiday: {'✅ PASS' if results['get_holiday']['success'] else '❌ FAIL'} ({response.status_code})")

            # Test update holiday
            print(f"\n✏️ Testing: Update holiday ({holiday_id})...")
            update_data = {
                "description": "Christmas Day - Updated"
            }
            response = await client.put(f"/api/settings/holidays/{holiday_id}", json=update_data, headers=self.auth_headers)
            results["update_holiday"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"Update Holiday: {'✅ PASS' if results['update_holiday']['success'] else '❌ FAIL'} ({response.status_code})")

            # Test delete holiday
            print(f"\n🗑️ Testing: Delete holiday ({holiday_id})...")
            response = await client.delete(f"/api/settings/holidays/{holiday_id}", headers=self.auth_headers)
            results["delete_holiday"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"Delete Holiday: {'✅ PASS' if results['delete_holiday']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test validation errors
        print("\n❌ Testing: Invalid holiday data...")
        invalid_data = {
            "holiday_type": "INVALID",
            "date": "invalid-date",
            "all_day": False
        }
        response = await client.post("/api/settings/holidays", json=invalid_data, headers=self.auth_headers)
        results["invalid_holiday"] = {
            "status_code": response.status_code,
            "success": response.status_code == 422,  # Should return validation error
            "response": self.safe_json_response(response)
        }
        print(f"Invalid Holiday: {'✅ PASS' if results['invalid_holiday']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test unauthorized access
        print("\n🔒 Testing: Unauthorized access...")
        response = await client.get("/api/settings/holidays")  # No auth headers
        results["unauthorized_access"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,
            "response": self.safe_json_response(response)
        }
        print(f"Unauthorized Access: {'✅ PASS' if results['unauthorized_access']['success'] else '❌ FAIL'} ({response.status_code})")

        return results

    async def test_conversation_logs_endpoints(self, client: AsyncClient):
        """Test conversation logs endpoints"""
        print("\n" + "="*50)
        print("💬 TESTING CONVERSATION LOGS ENDPOINTS")
        print("="*50)

        results = {}

        # Test list conversation logs (should require auth)
        response = await client.get("/api/v1/conversation-logs/")
        results["list_unauthorized"] = {
            "success": response.status_code == 401,
            "status_code": response.status_code,
            "response": self.safe_json_response(response)
        }
        print(f"List Unauthorized: {'✅ PASS' if results['list_unauthorized']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test with authentication
        if self.auth_headers:
            # Test list conversation logs
            response = await client.get("/api/v1/conversation-logs/", headers=self.auth_headers)
            results["list_authorized"] = {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response": self.safe_json_response(response)
            }
            print(f"List Authorized: {'✅ PASS' if results['list_authorized']['success'] else '❌ FAIL'} ({response.status_code})")

            # Test get conversation log by ID (non-existent)
            fake_id = "550e8400-e29b-41d4-a716-446655440000"
            response = await client.get(f"/api/v1/conversation-logs/{fake_id}", headers=self.auth_headers)
            results["get_nonexistent"] = {
                "success": response.status_code == 404,
                "status_code": response.status_code,
                "response": self.safe_json_response(response)
            }
            print(f"Get Non-existent: {'✅ PASS' if results['get_nonexistent']['success'] else '❌ FAIL'} ({response.status_code})")

            # Test get lead conversation history (non-existent lead)
            response = await client.get(f"/api/v1/conversation-logs/lead/{fake_id}", headers=self.auth_headers)
            results["get_lead_history"] = {
                "success": response.status_code in [200, 404],  # Either is acceptable
                "status_code": response.status_code,
                "response": self.safe_json_response(response)
            }
            print(f"Get Lead History: {'✅ PASS' if results['get_lead_history']['success'] else '❌ FAIL'} ({response.status_code})")

            # Test update conversation log (non-existent)
            update_data = {"message": "Updated message"}
            response = await client.put(f"/api/v1/conversation-logs/{fake_id}", json=update_data, headers=self.auth_headers)
            results["update_nonexistent"] = {
                "success": response.status_code == 404,
                "status_code": response.status_code,
                "response": self.safe_json_response(response)
            }
            print(f"Update Non-existent: {'✅ PASS' if results['update_nonexistent']['success'] else '❌ FAIL'} ({response.status_code})")

            # Test delete conversation log (non-existent)
            response = await client.delete(f"/api/v1/conversation-logs/{fake_id}", headers=self.auth_headers)
            results["delete_nonexistent"] = {
                "success": response.status_code == 404,
                "status_code": response.status_code,
                "response": self.safe_json_response(response)
            }
            print(f"Delete Non-existent: {'✅ PASS' if results['delete_nonexistent']['success'] else '❌ FAIL'} ({response.status_code})")

            # Test list with filters
            response = await client.get("/api/v1/conversation-logs/?sender=user&page=1&per_page=10", headers=self.auth_headers)
            results["list_with_filters"] = {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response": self.safe_json_response(response)
            }
            print(f"List with Filters: {'✅ PASS' if results['list_with_filters']['success'] else '❌ FAIL'} ({response.status_code})")

        return results

    async def run_all_tests(self):
        """Run all endpoint tests"""
        print("\n" + "="*70)
        print("🚀 GROWTHHIVE API MASTER TEST SUITE")
        print("="*70)
        print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        all_results = {}

        async with self.setup_client() as client:
            # Setup authentication
            await self.register_and_login(client)

            # Run all test suites
            all_results["authentication"] = await self.test_authentication_endpoints(client)
            all_results["categories"] = await self.test_categories_endpoints(client)
            all_results["subcategories"] = await self.test_subcategories_endpoints(client)
            all_results["franchisors"] = await self.test_franchisors_endpoints(client)
            all_results["leads"] = await self.test_leads_endpoints(client)
            all_results["documents"] = await self.test_documents_endpoints(client)
            all_results["holidays"] = await self.test_holidays_endpoints(client)
            all_results["logs"] = await self.test_logs_endpoints(client)
            all_results["conversation_logs"] = await self.test_conversation_logs_endpoints(client)

        # Print summary
        self.print_test_summary(all_results)
        return all_results

    def print_test_summary(self, results: Dict[str, Any]):
        """Print comprehensive test summary"""
        print("\n" + "="*70)
        print("📊 TEST SUMMARY")
        print("="*70)

        total_tests = 0
        passed_tests = 0

        for module, module_results in results.items():
            print(f"\n📂 {module.upper()} MODULE:")
            module_passed = 0
            module_total = 0

            for test_name, test_result in module_results.items():
                status = "✅ PASS" if test_result["success"] else "❌ FAIL"
                print(f"  {test_name}: {status} ({test_result['status_code']})")

                module_total += 1
                total_tests += 1

                if test_result["success"]:
                    module_passed += 1
                    passed_tests += 1

            print(f"  Module Summary: {module_passed}/{module_total} passed")

        print(f"\n🎯 OVERALL SUMMARY:")
        print(f"  Total Tests: {total_tests}")
        print(f"  Passed: {passed_tests}")
        print(f"  Failed: {total_tests - passed_tests}")
        print(f"  Success Rate: {(passed_tests/total_tests*100):.1f}%")

        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! 🎉")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} TESTS FAILED")

        print(f"\n🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*70)


# Test execution functions
async def run_master_test_suite():
    """Run the complete master test suite"""
    suite = MasterTestSuite()
    return await suite.run_all_tests()


# Pytest integration
@pytest_asyncio.fixture
async def master_test_suite():
    """Pytest fixture for master test suite"""
    return MasterTestSuite()


@pytest_asyncio.fixture
async def run_full_test_suite(master_test_suite):
    """Pytest fixture to run full test suite"""
    return await master_test_suite.run_all_tests()


# Main execution
if __name__ == "__main__":
    import asyncio

    print("🚀 Running GrowthHive Master Test Suite...")
    results = asyncio.run(run_master_test_suite())

    # Exit with appropriate code
    total_tests = sum(len(module_results) for module_results in results.values())
    passed_tests = sum(
        sum(1 for test_result in module_results.values() if test_result["success"])
        for module_results in results.values()
    )

    exit_code = 0 if passed_tests == total_tests else 1
    exit(exit_code)
